{"name": "rops-kanban", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --coverage", "watch": "jest --watch", "push": "./pushScript.sh", "safe-dev": "./safeDevScript.sh"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource-variable/roboto-slab": "^5.0.20", "@fontsource/roboto": "^5.0.8", "@hello-pangea/dnd": "^16.5.0", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.10", "@mui/x-charts": "^7.11.0", "@tanstack/react-query": "^5.66.9", "@vercel/analytics": "^1.4.1", "d3": "^7.9.0", "firebase": "^10.9.0", "mui-markdown": "^1.2.5", "next": "^14.1.0", "prop-types": "^15.8.1", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1"}, "proxy": "https://www.rops.io/", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "eslint": "8.57.1", "eslint-config-next": "15.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "overrides": {"tr46": "^4.0.0"}}