# LEGALWARE.AI

A Next.js based Kanban board application with Material UI styling and Firebase integration.

## Key Features

- Drag and drop Kanban board functionality using @hello-pangea/dnd
- Material UI components and theming
- Firebase backend integration
- Automated testing with Jest
- Analytics tracking with Vercel

## Development

### Setup

```
npm install
```

### Run Development Server

```
npm run dev
```

### Build for Production

```
npm run build
```

### Testing

```
npm run test
```

## Making Changes

All commits should be made using the provided push script to ensure tests pass before changes are pushed:

```
npm run push
```

The push script will:

1. Stage all changes
2. Receive the commit message
3. Run tests
4. Only push if all tests pass

## Tech Stack

- Next.js 14
- React 18
- Material UI
- Firebase
- Jest Testing Framework
- Vercel Analytics

## Project Structure

- /src/app - Main application code
- /src/app/core - Core application features
- /src/app/auth - Authentication related components
- /src/helpers - Shared utility functions

The project uses custom theming with Roboto Slab Variable font and an earthy pastel color palette for user assignments.

<!--
```

```
 -->
