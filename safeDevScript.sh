#!/bin/bash

# Check if production Firebase config is being used
if grep -q "const app = initializeApp(firebaseConfig)" "src/app/firebase.js"; then
    echo "###### ######################################################### ######"
    echo "###### ALERT: Production environment detected!                   ######"
    echo "###### Do you want to switch to development environment? (y/n):  ######"
    echo "###### ######################################################### ######"
    
    read -n 1 -r answer
    echo ""
    
    if [[ $answer =~ ^[Yy]$ ]]; then
        # Switch to development environment
        sed -i '' 's/const app = initializeApp(firebaseConfig)/const app = initializeApp(development)/g' src/app/firebase.js
        echo "✓ Switched to development environment."
    else
        echo "⚠ Continuing with production environment."
    fi
else
    echo "✓ Development environment already in use."
fi

# Run the dev script
npm run dev
