import React from "react"
import { render, screen } from "@testing-library/react"
import SplashScreen from "./SplashScreen"
import { Backdrop, CircularProgress } from "@mui/material"

// Mock Material UI components
jest.mock("@mui/material", () => ({
  Backdrop: jest.fn(({ children, ...props }) => (
    <div data-testid="backdrop" {...props}>
      {children}
    </div>
  )),
  CircularProgress: jest.fn(() => <div data-testid="circular-progress" />)
}))

describe("SplashScreen", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders without crashing", () => {
    render(<SplashScreen />)
    expect(screen.getByTestId("backdrop")).toBeInTheDocument()
  })

  it("renders Backdrop with correct props", () => {
    render(<SplashScreen />)

    expect(Backdrop).toHaveBeenCalledTimes(1)
    expect(Backdrop).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        sx: expect.objectContaining({
          color: "#fff",
          zIndex: expect.any(Function)
        })
      }),
      expect.anything()
    )
  })

  it("renders CircularProgress inside Backdrop", () => {
    render(<SplashScreen />)

    expect(CircularProgress).toHaveBeenCalledTimes(1)
    expect(CircularProgress).toHaveBeenCalledWith(
      expect.objectContaining({
        color: "inherit"
      }),
      expect.anything()
    )

    const backdrop = screen.getByTestId("backdrop")
    const circularProgress = screen.getByTestId("circular-progress")
    expect(backdrop).toContainElement(circularProgress)
  })

  it("passes the correct zIndex function to the Backdrop", () => {
    render(<SplashScreen />)
    const veryHighIndex = 1200

    // Extract the zIndex function from the Backdrop call
    const backdropProps = Backdrop.mock.calls[0][0]
    const zIndexFn = backdropProps.sx.zIndex

    // Mock theme object
    const mockTheme = {
      zIndex: {
        drawer: veryHighIndex
      }
    }

    // Test that the function returns drawer + 1
    expect(zIndexFn(mockTheme)).toBe(veryHighIndex + 1)
  })
})
