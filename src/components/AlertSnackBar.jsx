import React from "react"
import { <PERSON><PERSON>, Snackbar } from "@mui/material"
import { bool, func, number, shape, string } from "prop-types"

function AlertSnackbar({
  open,
  message,
  severity = "success",
  autoHideDuration = 3000,
  onClose,
  anchorOrigin = { vertical: "bottom", horizontal: "right" }
}) {
  const handleClose = (_event, reason) => {
    if (reason === "clickaway") return
    if (onClose) onClose()
  }

  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      anchorOrigin={anchorOrigin}
    >
      <Alert onClose={handleClose} severity={severity} variant="filled" sx={{ width: "100%" }}>
        {message}
      </Alert>
    </Snackbar>
  )
}

export default AlertSnackbar

AlertSnackbar.propTypes = {
  open: bool,
  message: string,
  severity: string,
  autoHideDuration: number,
  onClose: func,
  anchorOrigin: shape({
    vertical: string,
    horizontal: string
  })
}
