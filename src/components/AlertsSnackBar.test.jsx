import React from "react"
import { render, screen, fireEvent, act } from "@testing-library/react"
import AlertSnackbar from "./AlertSnackBar"

// Mock Material UI Snackbar to avoid timing issues in tests
jest.mock("@mui/material", () => {
  const actual = jest.requireActual("@mui/material")
  return {
    ...actual,
    Snackbar: ({ children, open, onClose }) => (
      <div data-testid="mock-snackbar" data-open={open}>
        <button data-testid="clickaway-button" onClick={() => onClose && onClose({}, "clickaway")}>
          Clickaway
        </button>
        <button data-testid="escape-button" onClick={() => onClose && onClose({}, "escapeKeyDown")}>
          Escape
        </button>
        {children}
      </div>
    ),
    Alert: ({ children, severity, onClose, variant }) => (
      <div data-testid="mock-alert" data-severity={severity} data-variant={variant}>
        {children}
        <button data-testid="alert-close-button" onClick={() => onClose && onClose()}>
          Close
        </button>
      </div>
    )
  }
})

describe("AlertSnackbar Component", () => {
  test("renders correctly with default props", () => {
    render(<AlertSnackbar open={true} message="Test message" />)

    const snackbar = screen.getByTestId("mock-snackbar")
    const alert = screen.getByTestId("mock-alert")

    expect(snackbar).toBeInTheDocument()
    expect(snackbar).toHaveAttribute("data-open", "true")
    expect(alert).toHaveAttribute("data-severity", "success")
    expect(alert).toHaveAttribute("data-variant", "filled")
    expect(alert).toHaveTextContent("Test message")
  })

  test("renders with custom severity", () => {
    render(<AlertSnackbar open={true} message="Error message" severity="error" />)

    const alert = screen.getByTestId("mock-alert")
    expect(alert).toHaveAttribute("data-severity", "error")
    expect(alert).toHaveTextContent("Error message")
  })

  test("does not call onClose when clicking away", () => {
    const handleClose = jest.fn()
    render(<AlertSnackbar open={true} message="Test message" onClose={handleClose} />)

    const clickawayButton = screen.getByTestId("clickaway-button")
    // Simulates clickaway
    fireEvent.click(clickawayButton)

    // The handleClose function in AlertSnackbar should not call onClose when reason is "clickaway"
    expect(handleClose).not.toHaveBeenCalled()
  })

  test("calls onClose when using escape key", () => {
    const handleClose = jest.fn()
    render(<AlertSnackbar open={true} message="Test message" onClose={handleClose} />)

    const escapeButton = screen.getByTestId("escape-button")
    // Simulates escape key
    fireEvent.click(escapeButton)

    expect(handleClose).toHaveBeenCalled()
  })

  test("calls onClose when clicking the alert close button", () => {
    const handleClose = jest.fn()
    render(<AlertSnackbar open={true} message="Test message" onClose={handleClose} />)

    const closeButton = screen.getByTestId("alert-close-button")
    // Simulates clicking the close button on the alert
    fireEvent.click(closeButton)

    expect(handleClose).toHaveBeenCalled()
  })

  test("respects custom autoHideDuration", () => {
    render(<AlertSnackbar open={true} message="Test message" autoHideDuration={5000} />)

    const snackbar = screen.getByTestId("mock-snackbar")
    expect(snackbar).toBeInTheDocument()
  })

  test("respects custom anchorOrigin", () => {
    const customAnchor = { vertical: "top", horizontal: "left" }
    render(<AlertSnackbar open={true} message="Test message" anchorOrigin={customAnchor} />)

    // We can't easily test the actual positioning in JSDOM, but we can verify the component renders
    const snackbar = screen.getByTestId("mock-snackbar")
    expect(snackbar).toBeInTheDocument()
  })

  test("handles closed state", () => {
    render(<AlertSnackbar open={false} message="Test message" />)

    const snackbar = screen.getByTestId("mock-snackbar")
    expect(snackbar).toHaveAttribute("data-open", "false")
  })

  test("handles undefined onClose prop", () => {
    render(<AlertSnackbar open={true} message="Test message" />)

    const closeButton = screen.getByTestId("alert-close-button")

    // This should not throw an error
    expect(() => {
      fireEvent.click(closeButton)
    }).not.toThrow()
  })
})
