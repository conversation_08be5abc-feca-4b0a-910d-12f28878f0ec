import { node, number, shape, object, func } from "prop-types"
import { Stack } from "@mui/material"

const SvgContainer = ({ children, dimensions, styles, onScroll, svgRef }) => {
  const containerWidth = dimensions.totalWidth || dimensions.width
  const containerHeight = dimensions.totalHeight || dimensions.height

  return (
    <Stack
      sx={{
        width: dimensions.totalWidth ? dimensions.width : "100%",
        height: dimensions.totalHeight ? dimensions.height : "100%"
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
          overflow: "auto",
          position: "relative"
        }}
        onScroll={onScroll}
      >
        <svg
          ref={svgRef}
          width={containerWidth}
          height={containerHeight}
          style={{
            display: "block",
            ...styles
          }}
        >
          {children}
        </svg>
      </div>
    </Stack>
  )
}

export default SvgContainer

SvgContainer.propTypes = {
  children: node,
  dimensions: shape({
    width: number.isRequired,
    height: number.isRequired,
    totalWidth: number,
    totalHeight: number
  }).isRequired,
  styles: object,
  onScroll: func,
  svgRef: shape({ current: object })
}
