import { render, fireEvent } from "@testing-library/react"
import SvgContainer from "./SvgContainer"

describe("SvgContainer", () => {
  const defaultProps = {
    dimensions: {
      width: 800,
      height: 600
    },
    children: (
      <g>
        <circle cx={50} cy={50} r={25} />
      </g>
    )
  }

  test("renders with minimum required props", () => {
    const { container } = render(<SvgContainer {...defaultProps} />)
    const svg = container.querySelector("svg")

    expect(svg).toBeInTheDocument()
    expect(svg.getAttribute("width")).toBe("800")
    expect(svg.getAttribute("height")).toBe("600")
  })

  test("renders with total dimensions", () => {
    const props = {
      ...defaultProps,
      dimensions: {
        width: 800,
        height: 600,
        totalWidth: 1200,
        totalHeight: 900
      }
    }

    const { container } = render(<SvgContainer {...props} />)
    const svg = container.querySelector("svg")

    expect(svg.getAttribute("width")).toBe("1200")
    expect(svg.getAttribute("height")).toBe("900")
  })

  test("applies custom styles", () => {
    const customStyles = {
      backgroundColor: "red",
      border: "1px solid black"
    }

    const { container } = render(<SvgContainer {...defaultProps} styles={customStyles} />)
    const svg = container.querySelector("svg")

    expect(svg).toHaveStyle(customStyles)
  })

  test("handles scroll events", () => {
    const onScroll = jest.fn()
    const { container } = render(<SvgContainer {...defaultProps} onScroll={onScroll} />)

    const scrollContainer = container.querySelector("div[style*='overflow: auto']")
    fireEvent.scroll(scrollContainer, { target: { scrollY: 100 } })

    expect(onScroll).toHaveBeenCalled()
  })

  test("forwards ref to svg element", () => {
    const svgRef = { current: null }
    const { container } = render(<SvgContainer {...defaultProps} svgRef={svgRef} />)

    const svg = container.querySelector("svg")
    expect(svgRef.current).toBe(svg)
  })

  test("renders children content", () => {
    const { container } = render(<SvgContainer {...defaultProps} />)
    const circle = container.querySelector("circle")

    expect(circle).toBeInTheDocument()
    expect(circle.getAttribute("cx")).toBe("50")
    expect(circle.getAttribute("cy")).toBe("50")
    expect(circle.getAttribute("r")).toBe("25")
  })
})
