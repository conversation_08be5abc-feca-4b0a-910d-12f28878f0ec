import { render } from "@testing-library/react"
import WrapText from "./WrapText"

describe("WrapText", () => {
  const defaultProps = {
    text: "This is a test text",
    width: 200,
    x: 10,
    y: 20
  }

  test("renders single line text", () => {
    const { container } = render(
      <svg>
        <WrapText {...defaultProps} />
      </svg>
    )
    const text = container.querySelector("text")
    const tspan = container.querySelector("tspan")

    expect(text).toBeInTheDocument()
    expect(tspan).toHaveTextContent("This is a test text")
  })

  test("wraps text into multiple lines", () => {
    const props = {
      ...defaultProps,
      text: "This is a very long text that should wrap into multiple lines",
      width: 50
    }

    const { container } = render(
      <svg>
        <WrapText {...props} />
      </svg>
    )
    const tspans = container.querySelectorAll("tspan")

    expect(tspans.length).toBeGreaterThan(1)
  })

  test("applies custom styles", () => {
    const styles = {
      fill: "red",
      fontSize: "14px"
    }

    const { container } = render(
      <svg>
        <WrapText {...defaultProps} styles={styles} />
      </svg>
    )

    const text = container.querySelector("text")
    expect(text).toHaveStyle(styles)
  })

  test("centers text when centered prop is true", () => {
    const { container } = render(
      <svg>
        <WrapText {...defaultProps} centered={true} />
      </svg>
    )

    const text = container.querySelector("text")
    expect(text).toHaveAttribute("text-anchor", "middle")
  })

  test("uses custom text height", () => {
    const { container } = render(
      <svg>
        <WrapText {...defaultProps} textHeight={20} />
      </svg>
    )

    const text = container.querySelector("text")
    expect(text).toHaveAttribute("y", defaultProps.y.toString())
  })

  test("handles empty text", () => {
    const props = {
      ...defaultProps,
      text: ""
    }

    const { container } = render(
      <svg>
        <WrapText {...props} />
      </svg>
    )
    const tspan = container.querySelector("tspan")

    expect(tspan).toHaveTextContent("")
  })

  test("handles single word text", () => {
    const props = {
      ...defaultProps,
      text: "Supercalifragilisticexpialidocious",
      // Increased width to accommodate long word
      width: 300
    }

    const { container } = render(
      <svg>
        <WrapText {...props} />
      </svg>
    )
    const tspans = container.querySelectorAll("tspan")

    expect(tspans.length).toBe(1)
    expect(tspans[0]).toHaveTextContent("Supercalifragilisticexpialidocious")
  })
})
