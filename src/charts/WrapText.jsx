import { string, number, object, bool } from "prop-types"

const WrapText = ({ text, width = 70, x, y, textHeight = 10, styles = {}, centered = false }) => {
  const estimatedWidthPerCharacter = 6

  const wrappedText = (() => {
    const words = text.split(" ")
    const lines = []
    let currentLine = words[0]

    for (let i = 1; i < words.length; i++) {
      const word = words[i]
      const testLine = `${currentLine} ${word}`
      const testWidth = testLine.length * estimatedWidthPerCharacter

      if (testWidth < width) {
        currentLine = testLine
      } else {
        lines.push(currentLine)
        currentLine = word
      }
    }
    lines.push(currentLine)
    return lines
  })()

  // Calculate the starting y position to center the text block vertically
  const startY = y - (wrappedText.length - 1) * textHeight

  return (
    <text
      x={x}
      y={startY}
      style={styles}
      textAnchor={centered ? "middle" : "start"}
      dominantBaseline="hanging"
    >
      {wrappedText.map((line, lineIndex) => (
        <tspan key={line + lineIndex} x={x} dy={lineIndex === 0 ? 0 : "1.2em"}>
          {line}
        </tspan>
      ))}
    </text>
  )
}

WrapText.propTypes = {
  text: string.isRequired,
  width: number.isRequired,
  textHeight: number,
  x: number.isRequired,
  y: number.isRequired,
  styles: object,
  centered: bool
}

export default WrapText
