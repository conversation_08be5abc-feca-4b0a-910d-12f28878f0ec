import { render } from "@testing-library/react"
import <PERSON>vineLogo from "./FilevineLogo"

describe("FilevineLogo", () => {
  test("renders logo with correct dimensions", () => {
    const { container } = render(<FilevineLogo />)
    const svg = container.querySelector("svg")

    expect(svg).toBeInTheDocument()
    expect(svg).toHaveAttribute("width", "97.82")
    expect(svg).toHaveAttribute("height", "25")
    expect(svg).toHaveAttribute("viewBox", "0 0 211.46 54.04")
  })

  test("includes aria-hidden attribute", () => {
    const { container } = render(<FilevineLogo />)
    const svg = container.querySelector("svg")

    expect(svg).toHaveAttribute("aria-hidden", "true")
  })

  test("renders all path elements", () => {
    const { container } = render(<FilevineLogo />)
    const paths = container.querySelectorAll("path")

    expect(paths).toHaveLength(9)
    paths.forEach((path) => {
      expect(path).toHaveAttribute("d")
    })
  })
})
