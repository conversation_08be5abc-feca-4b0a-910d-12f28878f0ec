import { useState, useEffect } from "react"
import { useQueryClient } from "@tanstack/react-query"

function QueryDebugger() {
  const queryClient = useQueryClient()
  const [queries, setQueries] = useState([])

  // Update queries state whenever cache changes
  useEffect(() => {
    // Function to get the latest queries
    const updateQueries = () => {
      setQueries(queryClient.getQueryCache().getAll())
    }

    // Get initial queries
    updateQueries()

    // Subscribe to query cache changes
    const unsubscribe = queryClient.getQueryCache().subscribe(() => {
      updateQueries()
    })

    // Clean up subscription
    return () => {
      unsubscribe()
    }
  }, [queryClient])

  return (
    <div style={{ margin: "20px 20px 20px 80px", padding: "20px", border: "1px solid #ccc" }}>
      <h3>React Query Cache Contents ({queries.length} queries)</h3>
      <ul>
        {queries.map((query) => (
          <li key={JSON.stringify(query.queryKey)}>
            <details>
              <summary>
                <strong>{JSON.stringify(query.queryKey)}</strong> - Status: {query.state.status}
              </summary>
              <pre>{JSON.stringify(query.state.data, null, 2)}</pre>
            </details>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default QueryDebugger
