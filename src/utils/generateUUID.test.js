import generateUUID from "./generateUUID"

describe("generateUUID", () => {
  test("should return a string", () => {
    const uuid = generateUUID()
    expect(typeof uuid).toBe("string")
  })

  test("should generate a UUID with the correct format", () => {
    const uuid = generateUUID()
    // UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx where x is any hex digit and y is 8, 9, a, or b
    expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/)
  })

  test("should generate unique UUIDs", () => {
    const uuid1 = generateUUID()
    const uuid2 = generateUUID()
    const uuid3 = generateUUID()

    expect(uuid1).not.toEqual(uuid2)
    expect(uuid1).not.toEqual(uuid3)
    expect(uuid2).not.toEqual(uuid3)
  })

  test("should have the correct version (4)", () => {
    const position = 14
    const uuid = generateUUID()
    // The 13th character should be '4' for version 4 UUIDs
    expect(uuid.charAt(position)).toBe("4")
  })

  test("should have the correct variant (10xx in binary)", () => {
    const position = 19
    const uuid = generateUUID()
    // The 17th character should be 8, 9, a, or b for the correct variant
    expect("89ab").toContain(uuid.charAt(position))
  })

  test("should have the correct length of 36 characters", () => {
    const totalLength = 36
    const uuid = generateUUID()
    expect(uuid.length).toBe(totalLength)
  })

  test("should have hyphens in the correct positions", () => {
    const hyphenPosition1 = 8
    const hyphenPosition2 = 13
    const hyphenPosition3 = 18
    const hyphenPosition4 = 23
    const uuid = generateUUID()
    expect(uuid.charAt(hyphenPosition1)).toBe("-")
    expect(uuid.charAt(hyphenPosition2)).toBe("-")
    expect(uuid.charAt(hyphenPosition3)).toBe("-")
    expect(uuid.charAt(hyphenPosition4)).toBe("-")
  })
})
