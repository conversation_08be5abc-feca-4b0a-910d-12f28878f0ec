import {
  useProjectList,
  useProjectTypeList,
  useProjectPhaseList,
  useProjectAttributes,
  useTenant
} from "./firebaseGetMethods"
import * as firebaseFunctions from "firebase/functions"
import { useQuery } from "@tanstack/react-query"
import { renderHook } from "@testing-library/react"

// Mock Firebase functions
jest.mock("firebase/functions", () => {
  return {
    httpsCallable: jest.fn(() => jest.fn()),
    getFunctions: jest.fn(() => "mockedFunctions")
  }
})

// Mock React Query
jest.mock("@tanstack/react-query", () => ({
  useQuery: jest.fn()
}))

// Mock the app import
jest.mock("../app/firebase", () => "mockedFirebaseApp")

describe("Firebase Get Methods", () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Default mock implementation for useQuery
    useQuery.mockImplementation(() => ({
      data: null,
      isLoading: false,
      error: null
    }))
  })

  describe("useProjectList", () => {
    const mockProjectListData = { projects: [{ id: 1, name: "Project 1" }] }
    const mockProjectListCaller = jest.fn().mockResolvedValue({ data: mockProjectListData })

    beforeEach(() => {
      firebaseFunctions.httpsCallable.mockReturnValue(mockProjectListCaller)
    })

    it("calls useQuery with correct parameters", () => {
      const queryOptions = { staleTime: 5000 }
      renderHook(() => useProjectList(queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["projectList"],
        queryFn: expect.any(Function),
        staleTime: 5000
      })
    })

    it("returns project data when API call succeeds", async () => {
      renderHook(() => useProjectList())
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(mockProjectListCaller).toHaveBeenCalledWith({ hashtag: "" })
      expect(result).toEqual(mockProjectListData)
    })

    it("throws error when API response is missing data", async () => {
      mockProjectListCaller.mockResolvedValueOnce({ data: null })
      renderHook(() => useProjectList())
      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch project list")
    })
  })

  describe("useProjectTypeList", () => {
    const mockTypeListData = { items: [{ id: 1, name: "Type 1" }] }
    const mockTypeListCaller = jest.fn().mockResolvedValue({ data: mockTypeListData })

    beforeEach(() => {
      firebaseFunctions.httpsCallable.mockReturnValue(mockTypeListCaller)
    })

    it("calls useQuery with correct parameters", () => {
      const queryOptions = { refetchOnWindowFocus: false }
      renderHook(() => useProjectTypeList(queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["projectTypeList"],
        queryFn: expect.any(Function),
        refetchOnWindowFocus: false
      })
    })

    it("returns type list data when API call succeeds", async () => {
      renderHook(() => useProjectTypeList())
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(mockTypeListCaller).toHaveBeenCalled()
      expect(result).toEqual(mockTypeListData.items)
    })

    it("throws error when API response is missing items", async () => {
      mockTypeListCaller.mockResolvedValueOnce({ data: {} })
      renderHook(() => useProjectTypeList())
      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch project type list")
    })
  })

  describe("useProjectPhaseList", () => {
    const mockPhaseListData = { items: [{ id: 1, name: "Phase 1" }] }
    const mockPhaseListCaller = jest.fn().mockResolvedValue({ data: mockPhaseListData })

    beforeEach(() => {
      firebaseFunctions.httpsCallable.mockReturnValue(mockPhaseListCaller)
    })

    it("calls useQuery with correct parameters and projectTypeId", () => {
      const queryOptions = { refetchInterval: 10000 }
      renderHook(() => useProjectPhaseList("type-123", queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["projectPhaseList", { projectTypeId: "type-123" }],
        queryFn: expect.any(Function),
        enabled: true,
        refetchInterval: 10000
      })
    })

    it("correctly sets enabled option based on projectTypeId", () => {
      renderHook(() => useProjectPhaseList(null))
      expect(useQuery.mock.calls[0][0].enabled).toBe(false)

      renderHook(() => useProjectPhaseList("type-123"))
      expect(useQuery.mock.calls[1][0].enabled).toBe(true)
    })

    it("returns phase list data when API call succeeds", async () => {
      renderHook(() => useProjectPhaseList("type-123"))
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(mockPhaseListCaller).toHaveBeenCalledWith({ projectTypeId: "type-123" })
      expect(result).toEqual(mockPhaseListData.items)
    })

    it("throws error when API response is missing items", async () => {
      mockPhaseListCaller.mockResolvedValueOnce({ data: {} })
      renderHook(() => useProjectPhaseList("type-123"))
      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch project phase list")
    })
  })

  describe("useProjectAttributes", () => {
    const mockAttributesData = { id: 1, status: "Active" }
    const mockAttributesCaller = jest.fn().mockResolvedValue({ data: mockAttributesData })

    beforeEach(() => {
      firebaseFunctions.httpsCallable.mockReturnValue(mockAttributesCaller)
    })

    it("calls useQuery with correct parameters", () => {
      const queryOptions = { cacheTime: 3000 }
      renderHook(() => useProjectAttributes("project-123", queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["projectVitals", { projectId: "project-123" }],
        queryFn: expect.any(Function),
        enabled: true,
        cacheTime: 3000
      })
    })

    it("returns null when projectId is not provided", async () => {
      renderHook(() => useProjectAttributes(null))
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(result).toBeNull()
      expect(mockAttributesCaller).not.toHaveBeenCalled()
    })

    it("correctly sets enabled option based on projectId", () => {
      renderHook(() => useProjectAttributes(null))
      expect(useQuery.mock.calls[0][0].enabled).toBe(false)

      renderHook(() => useProjectAttributes("project-123"))
      expect(useQuery.mock.calls[1][0].enabled).toBe(true)
    })

    it("returns attributes data when API call succeeds", async () => {
      renderHook(() => useProjectAttributes("project-123"))
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(mockAttributesCaller).toHaveBeenCalledWith({ projectId: "project-123" })
      expect(result).toEqual(mockAttributesData)
    })

    it("throws error when API response is missing data", async () => {
      mockAttributesCaller.mockResolvedValueOnce({ data: null })
      renderHook(() => useProjectAttributes("project-123"))
      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch project attributes")
    })
  })

  // Add tests for useTenant to achieve 100% coverage
  describe("useTenant", () => {
    const mockTenantData = {
      userId: "user123",
      name: "Test User",
      userData: { defaultSettings: { theme: "dark" } }
    }
    const mockTenantCaller = jest.fn().mockResolvedValue({ data: mockTenantData })

    beforeEach(() => {
      firebaseFunctions.httpsCallable.mockReturnValue(mockTenantCaller)
    })

    it("calls useQuery with correct parameters", () => {
      const queryOptions = { staleTime: 30000 }
      renderHook(() => useTenant(queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["userList"],
        queryFn: expect.any(Function),
        staleTime: 30000
      })
    })

    it("returns tenant data when API call succeeds", async () => {
      renderHook(() => useTenant())
      const queryFn = useQuery.mock.calls[0][0].queryFn
      const result = await queryFn()

      expect(mockTenantCaller).toHaveBeenCalled()
      expect(result).toEqual(mockTenantData)
    })

    it("throws error when API response is missing data", async () => {
      mockTenantCaller.mockResolvedValueOnce({ data: null })
      renderHook(() => useTenant())
      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch user list")
    })

    it("passes options to useQuery correctly", () => {
      const queryOptions = {
        retry: 3,
        refetchOnWindowFocus: false,
        refetchOnMount: false
      }

      renderHook(() => useTenant(queryOptions))

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["userList"],
        queryFn: expect.any(Function),
        retry: 3,
        refetchOnWindowFocus: false,
        refetchOnMount: false
      })
    })
  })
})
