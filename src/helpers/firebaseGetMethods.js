import { httpsCallable, getFunctions } from "firebase/functions"
import { useQuery } from "@tanstack/react-query"
import app from "@/app/firebase"

const functions = getFunctions(app)

export function useProjectList(queryOptions) {
  return useQuery({
    queryKey: ["projectList"],
    queryFn: async () => {
      const ProjectListCaller = httpsCallable(functions, "getprojects")
      const response = await ProjectListCaller({ hashtag: "" })

      if (!response.data) {
        throw new Error("Failed to fetch project list")
      }
      return response.data
    },
    ...queryOptions
  })
}

export function useProjectTypeList(queryOptions) {
  return useQuery({
    queryKey: ["projectTypeList"],
    queryFn: async () => {
      const projectTypeListCaller = httpsCallable(functions, "getprojecttypelist")
      const response = await projectTypeListCaller()

      if (!response.data?.items) {
        throw new Error("Failed to fetch project type list")
      }
      return response.data.items
    },
    ...queryOptions
  })
}

export function useProjectPhaseList(projectTypeId, queryOptions) {
  return useQuery({
    queryKey: ["projectPhaseList", { projectTypeId: String(projectTypeId) }],
    queryFn: async () => {
      const projectTypePhaseListCaller = httpsCallable(functions, "getprojecttypephaselist")
      const response = await projectTypePhaseListCaller({ projectTypeId })

      if (!response.data?.items) {
        throw new Error("Failed to fetch project phase list")
      }
      return response.data.items
    },
    // Only run the query if projectTypeId exists
    enabled: !!projectTypeId,
    ...queryOptions
  })
}

export function useProjectAttributes(projectId, queryOptions) {
  return useQuery({
    queryKey: ["projectVitals", { projectId: String(projectId) }],
    queryFn: async () => {
      if (!projectId) {
        return null
      }

      const projectVitalsCaller = httpsCallable(functions, "getprojectvitals")
      const response = await projectVitalsCaller({ projectId })

      if (!response.data) {
        throw new Error("Failed to fetch project attributes")
      }
      return response.data
    },
    // Only run the query if projectId exists
    enabled: !!projectId,
    ...queryOptions
  })
}

export const useTenant = (queryOptions) => {
  return useQuery({
    queryKey: ["userList"],
    queryFn: async () => {
      const userListCaller = httpsCallable(functions, "getuser")
      const response = await userListCaller()
      if (!response.data) {
        throw new Error("Failed to fetch user list")
      }
      return response.data
    },
    ...queryOptions
  })
}

export function useFieldHistory(projectTypeId, queryOptions) {
  return useQuery({
    queryKey: ["fieldHistory", { projectTypeId: String(projectTypeId) }],
    queryFn: async () => {
      const fieldHistoryCaller = httpsCallable(functions, "getProjectTypeFormStructure")
      const response = await fieldHistoryCaller({ projectTypeId })

      if (!response.data) {
        throw new Error("Failed to fetch field history")
      }
      return response.data
    },
    enabled: !!projectTypeId,
    ...queryOptions
  })
}
