"use client"
import { Inter } from "next/font/google"
import { Analytics } from "@vercel/analytics/next"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { node } from "prop-types"
import QueryDebugger from "@/utils/QueryDebugger"

const inter = Inter({ subsets: ["latin"] })
const queryClient = new QueryClient()

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <title>legalware.ai</title>
      </head>
      <body className={inter.className} suppressHydrationWarning={true}>
        <QueryClientProvider client={queryClient}>
          {children}
          {process.env.NEXT_PUBLIC_SHOW_QUERY_DEBBUGER === "true" && <QueryDebugger />}
        </QueryClientProvider>
        <Analytics debug={false} />
      </body>
    </html>
  )
}

RootLayout.propTypes = {
  children: node.isRequired
}
