import { createTheme } from "@mui/material/styles"
// Supports weights 100-900
import '@fontsource-variable/roboto-slab';

const defaultTheme = createTheme({
    typography: {
        h3: {
            fontSize: 23,
            fontWeight: 400,
            fontFamily: "Roboto Slab Variable, serif"
        },
        h4: {
            fontSize: 20,
            fontWeight: 600
        },
        h5: {
            fontSize: 19,
            fontWeight: 400,
            fontFamily: "Roboto Slab Variable, serif"
        },
        h6: {
            fontSize: 17,
            fontWeight: 600
        },
        h7: {
            fontSize: 16,
            fontWeight: 500
        },
        subtitle1: {
            fontSize: 15,
            fontWeight: 600,
            color: "gray"
        },
        subtitle2: {
            fontSize: 13,
            fontWeight: 200,
            color: "gray"
        },
        button: {
            padding: "15px",
            color: "gray",
            cursor: "pointer"
        },
        buttonSecondary: {
            padding: "5px 8px",
            color: "#0c2d5f",
            backgroundColor: "#e1b153",
            borderRadius: "3px",
            cursor: "pointer",
            fontFamily: "Roboto Slab Variable, serif"
        }
    },
    palette: {
        primary: {
            main: "#0c2d5f"
        },
        secondary: {
            main: "#e1b153"
        }
    }
})

module.exports = { defaultTheme }