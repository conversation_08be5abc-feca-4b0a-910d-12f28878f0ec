"use client"

import React, { useState, useEffect } from "react"
import { useSignInWithEmailAndPassword, useAuthState } from "react-firebase-hooks/auth"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { TextField, Button, Typography, Stack } from "@mui/material"
import { auth } from "../firebase"
import { ThemeProvider } from "@mui/material/styles"
import SplashScreen from "@/components/SplashScreen"

import { defaultTheme } from "../mui-theme"

const Login = () => {
  const [existingUser] = useAuthState(auth)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [signInWithEmailAndPassword, user, loading, error] = useSignInWithEmailAndPassword(auth)
  const router = useRouter()

  useEffect(() => {
    if (user || existingUser) {
      router.push("/core/kanban")
    }
  }, [user, existingUser, router])

  if (loading) {
    return <SplashScreen />
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      signInWithEmailAndPassword(email, password)
    }
  }

  return (
    <ThemeProvider theme={defaultTheme}>
      <Stack
        gap={2}
        sx={{
          width: 300,
          margin: "32px auto",
          padding: "16px 24px",
          borderRadius: "sm",
          boxShadow: "md"
        }}
        onKeyDown={handleKeyDown}
      >
        <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
          <Image src="/legalware.png" width={100} height={70} alt="legalware.ai company logo" />
          <Typography variant="h3" color="secondary">
            legalware.ai
          </Typography>
        </div>
        <TextField id="email" label="Email" variant="outlined" onChange={(e) => setEmail(e.target.value)} />
        <TextField
          id="password"
          label="Password"
          type="password"
          autoComplete="current-password"
          variant="outlined"
          onChange={(e) => setPassword(e.target.value)}
        />
        <Button variant="contained" onClick={() => signInWithEmailAndPassword(email, password)}>
          Login
        </Button>
        {error && <p>incorrect username or password</p>}
        <Typography
          variant="button"
          onClick={() => {
            router.push("/auth/forgot-password")
          }}
        >
          Forgot Password?
        </Typography>
      </Stack>
    </ThemeProvider>
  )
}

export default Login
