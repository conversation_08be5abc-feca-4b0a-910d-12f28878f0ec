"use client";

import React, { useState, useEffect } from "react";
import { useSignInWithEmailAndPassword } from "react-firebase-hooks/auth";
import { useRouter } from "next/navigation";
import { Box, TextField, Button, Typography } from "@mui/material";
import { sendPasswordResetEmail } from "firebase/auth";
import { auth } from "../../firebase";
import { ThemeProvider } from "@mui/material/styles";

import { defaultTheme } from "../../mui-theme";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [passwordResetSuccessful, setPasswordResetSuccessful] = useState(false);

  const router = useRouter();

  return (
    <ThemeProvider theme={defaultTheme}>
      <Box
        sx={{
          width: 300,
          mx: "auto",
          my: 4,
          py: 3,
          px: 2,
          display: "flex",
          flexDirection: "column",
          gap: 2,
          borderRadius: "sm",
          boxShadow: "md",
        }}
      >
        <TextField
          id="email"
          label="Email"
          variant="outlined"
          onChange={(e) => setEmail(e.target.value)}
        />
        {passwordResetSuccessful ? (
          <div>
            <Typography>
              Password Reset Email Sent, Please Go Back to the Login Page
            </Typography>
            <Button
              variant="contained"
              onClick={() => {
                router.push("/auth");
              }}
            >
              Go Back
            </Button>
          </div>
        ) : (
          <Button
            variant="contained"
            onClick={() => {
              sendPasswordResetEmail(auth, email)
                .then((data) => {
                  console.log(data, "password reset sent");
                  setPasswordResetSuccessful(true);
                })
                .catch((err) => console.log(err, "error resetting password"));
            }}
          >
            Send Password Reset
          </Button>
        )}
      </Box>
    </ThemeProvider>
  );
}
