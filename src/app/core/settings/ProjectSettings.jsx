import { NativeSelect, Box, Typography, LinearProgress, CircularProgress, Stack } from "@mui/material"
import { useQueryClient, useIsFetching } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { useUpdateTenant } from "./firebaseMethods.js/postMethods"
import { useTenant } from "@/helpers/firebaseGetMethods"
import AlertSnackbar from "@/components/AlertSnackBar"

const ProjectSettings = () => {
  const [defaultProjectType, setDefaultProjectType] = useState("")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [tenantList, setTenantList] = useState([])
  const [currentTenant, setCurrentTenant] = useState("")

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"])
  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const { mutate: updateTenant, isPending: isUpdatingUser } = useUpdateTenant()
  const isProjectTypeListLoading = useIsFetching(["projectTypeList"]) > 0
  const isTenantProjectTypesLoading = useIsFetching(["projectTypeList", currentTenant]) > 0

  const isLoading = isTenantLoading || isProjectTypeListLoading || isTenantProjectTypesLoading

  useEffect(
    function setDefaultTypeIfNotProvided() {
      if (
        !isLoading &&
        projectTypeList?.length > 0 &&
        tenantData?.userData?.tenant &&
        !tenantData?.userData?.defaultProjectType[tenantData?.userData?.tenant]
      ) {
        setAlertInfo({
          open: true,
          message: "We are setting a default project type for you. Please wait.",
          severity: "success"
        })
        updateTenant(
          {
            defaultProjectType: {
              ...tenantData?.userData?.defaultProjectType,
              [tenantData?.userData?.tenant]: projectTypeList[0].projectTypeId.native
            },
            tenant: tenantData?.userData?.tenant
          },
          {
            onSuccess: () => {
              window.location.reload()
            },
            onError: (error) =>
              setAlertInfo({
                open: true,
                message: `Something went wrong. Please try again later, ${error.message}`,
                severity: "error"
              })
          }
        )
      }
    },
    [tenantData, projectTypeList, isLoading, updateTenant]
  )

  useEffect(
    function initializeTenantValues() {
      const tenantExists = tenantData?.userData?.tenantAccessList?.length > 0 || tenantData?.userData?.tenant
      if (!isTenantLoading && tenantExists && tenantData?.userData?.defaultProjectType) {
        setTenantList(tenantData.userData.tenantAccessList ?? [tenantData.userData.tenant])
        setCurrentTenant(tenantData.userData.tenant)
        const projectTypeIdFromDB = tenantData.userData.defaultProjectType[tenantData.userData.tenant]
        setDefaultProjectType(Number(projectTypeIdFromDB))
      }
    },
    [isTenantLoading, tenantData]
  )

  const selectProjectType = (event) => {
    setDefaultProjectType(event.target.value)
    updateTenant(
      {
        defaultProjectType: {
          ...tenantData?.userData?.defaultProjectType,
          [tenantData?.userData?.tenant]: event.target.value
        },
        tenant: currentTenant
      },
      {
        onSuccess: () => {
          setAlertInfo({
            open: true,
            message: `The default project type has been updated successfully`,
            severity: "success"
          })

          // Additionally force refetch after successful update
          queryClient.refetchQueries(["projectTypeList"])
        },
        onError: (error) =>
          setAlertInfo({
            open: true,
            message: `An error occurred: ${error.message}`,
            severity: "error"
          })
      }
    )
  }

  const selectUserTenant = (event) => {
    const newTenant = event.target.value

    setCurrentTenant(newTenant)
    // Invalidate the query before changing the tenant
    queryClient.invalidateQueries({
      queryKey: ["projectTypeList"],
      exact: true
    })

    updateTenant(
      {
        defaultProjectType: {
          ...tenantData?.userData?.defaultProjectType
        },
        tenant: newTenant
      },
      {
        onSuccess: () => {
          setAlertInfo({
            open: true,
            message: `The tenant has been updated to ${newTenant}`,
            severity: "success"
          })

          // Additionally force refetch after successful update
          queryClient.refetchQueries(["projectTypeList", newTenant])
        },
        onError: (error) =>
          setAlertInfo({
            open: true,
            message: `An error occurred: ${error.message}`,
            severity: "error"
          })
      }
    )
  }

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  if (isTenantLoading || tenantData?.tenantAccessList?.length === 0) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Stack direction="column" sx={{ margin: "25px 0", height: "calc(100vh - 400px)" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Project Settings
      </Typography>

      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        Configure your default project type and tenant settings.
      </Typography>

      {(isUpdatingUser || isProjectTypeListLoading || isTenantProjectTypesLoading) && (
        <LinearProgress color="primary" sx={{ marginTop: "16px", marginBottom: "16px" }} />
      )}

      {tenantData?.userData?.tenantAccessList != null && (
        <>
          <Typography variant="h6">Select Tenant:</Typography>
          <NativeSelect
            sx={{
              width: "200px",
              marginBottom: "30px"
            }}
            onChange={selectUserTenant}
            value={currentTenant}
            disabled={isUpdatingUser || isProjectTypeListLoading}
          >
            {tenantList.map((tenant) => (
              <option key={tenant} value={tenant}>
                {tenant}
              </option>
            ))}
          </NativeSelect>
        </>
      )}

      <Typography variant="h6">Default Project Type:</Typography>
      <NativeSelect
        sx={{
          width: "200px",
          marginBottom: "60px"
        }}
        onChange={selectProjectType}
        value={defaultProjectType}
        disabled={isUpdatingUser || isProjectTypeListLoading}
      >
        {projectTypeList?.map((type) => (
          <option key={type.projectTypeId.native} value={type.projectTypeId.native}>
            {type.name}
          </option>
        ))}
      </NativeSelect>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default ProjectSettings
