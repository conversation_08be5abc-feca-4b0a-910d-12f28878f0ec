import { useState, useEffect, useMemo } from "react"
import { Box, Typography, LinearProgress, Stack, NativeSelect, TextField } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import { useFieldHistory } from "@/helpers/firebaseGetMethods"
import AlertSnackbar from "@/components/AlertSnackBar"

import RenderFormStructure from "./RenderFormStructure"

const FormHistorySettings = () => {
  const [selectedProjectType, setSelectedProjectType] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"]) || []

  const { data: sectionStructure, isLoading, isError, error } = useFieldHistory(selectedProjectType)

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleProjectTypeChange = (event) => {
    const projectTypeId = event.target.value
    setSelectedProjectType(projectTypeId)
    // Reset search when changing project type
    setSearchTerm("")
  }

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
  }

  // Client-side filtering of sections and fields based on search term
  const filteredSectionStructure = useMemo(() => {
    if (!sectionStructure || !searchTerm.trim()) {
      return sectionStructure
    }

    const searchLower = searchTerm.toLowerCase().trim()

    if (!sectionStructure.sections || !Array.isArray(sectionStructure.sections)) {
      return sectionStructure
    }

    const filteredSections = sectionStructure.sections
      .map((section) => {
        const sectionName = section.name || section.sectionSelector || ""
        const sectionMatches = sectionName.toLowerCase().includes(searchLower)

        // If section name matches, include all fields
        if (sectionMatches) {
          return section
        }

        // Otherwise, filter fields within the section
        if (section.formFields && Array.isArray(section.formFields)) {
          const filteredFields = section.formFields.filter((field) => {
            const fieldName = field.name || `Field ${field.id}` || ""
            return fieldName.toLowerCase().includes(searchLower)
          })

          // Only include section if it has matching fields
          if (filteredFields.length > 0) {
            return {
              ...section,
              formFields: filteredFields
            }
          }
        }

        return null
      })
      .filter(Boolean) // Remove null sections

    return {
      ...sectionStructure,
      sections: filteredSections
    }
  }, [sectionStructure, searchTerm])

  useEffect(() => {
    if (isError) {
      setAlertInfo({
        open: true,
        message: `Error loading field history: ${error?.message || "Unknown error"}`,
        severity: "error"
      })
    }
  }, [isError, error])

  if (projectTypeList.length === 0) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Stack direction="column" sx={{ margin: "50px", height: "calc(100vh - 450px)" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Field History
      </Typography>

      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        Select a project type to see its field structure.
      </Typography>

      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ width: "100%", marginBottom: "30px" }}
        spacing={2}
      >
        <NativeSelect sx={{ width: 200 }} value={selectedProjectType} onChange={handleProjectTypeChange}>
          <option value="">Select Project Type</option>
          {projectTypeList.map((projectType) => (
            <option key={projectType.projectTypeId.native} value={projectType.projectTypeId.native}>
              {projectType.name}
            </option>
          ))}
        </NativeSelect>

        {selectedProjectType /* && filteredSectionStructure?.sections?.length !== 0 && searchTerm */ && (
          <TextField
            placeholder="Search fields..."
            value={searchTerm}
            onChange={handleSearchChange}
            variant="standard"
            sx={{
              width: 200,
              "& .MuiInput-root": {
                fontSize: "1rem",
                "&:before": {
                  borderBottomColor: "rgba(0, 0, 0, 0.42)"
                },
                "&:hover:not(.Mui-disabled):before": {
                  borderBottomColor: "rgba(0, 0, 0, 0.87)"
                }
              },
              "& .MuiInput-input": {
                padding: "4px 0 5px"
              }
            }}
          />
        )}
      </Stack>

      <Stack
        flexGrow={1}
        sx={{
          overflowY: "auto",
          paddingRight: "8px",
          paddingBottom: "20px",
          "&::-webkit-scrollbar": {
            width: "8px"
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#a8a8a8"
          }
        }}
      >
        {(() => {
          if (!selectedProjectType) {
            return (
              <Typography
                variant="body1"
                sx={{ textAlign: "center", padding: "40px", color: "text.secondary" }}
              >
                Please select a project type to view its field structure.
              </Typography>
            )
          }

          if (isLoading) {
            return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
          }

          if (!sectionStructure) {
            return (
              <Typography
                variant="body2"
                sx={{ textAlign: "center", padding: "40px", fontStyle: "italic", color: "text.secondary" }}
              >
                No field structure available for this project type.
              </Typography>
            )
          }

          // Show message when search returns no results
          if (searchTerm.trim() && filteredSectionStructure?.sections?.length === 0) {
            return (
              <Typography
                variant="body2"
                sx={{ textAlign: "center", padding: "40px", fontStyle: "italic", color: "text.secondary" }}
              >
                No fields found matching &ldquo;{searchTerm}&rdquo;.
              </Typography>
            )
          }

          return (
            <Box sx={{ padding: "10px 0" }}>
              <RenderFormStructure sectionStructure={filteredSectionStructure} />
            </Box>
          )
        })()}
      </Stack>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default FormHistorySettings
