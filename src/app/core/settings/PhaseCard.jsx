import { Box, Typography, TextField, Switch } from "@mui/material"
import { Draggable } from "@hello-pangea/dnd"
import { number, func, shape, oneOfType, object, string, bool } from "prop-types"

const PhaseCard = ({ phase, index, setGoal, setDeadline, toggleUsePhase }) => {
  return (
    <Draggable key={phase.phaseId.native} draggableId={phase.draggableId} index={index}>
      {(provided) => (
        <Box
          sx={{
            margin: "15px",
            padding: "15px",
            boxShadow: "2px 4px 3px #aaa",
            bgcolor: "white",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          }}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
        >
          <Typography sx={{ width: "250px" }}>{phase.name}</Typography>
          {(phase.usePhase || "usePhase" in phase === false) && (
            <>
              <TextField
                id="goal-limit-input"
                label="Goal Duration (Days)"
                type="number"
                variant="standard"
                InputLabelProps={{
                  shrink: true
                }}
                onChange={(event) => setGoal(event, index)}
                value={phase.goal}
              />
              <TextField
                id="deadline-limit-input"
                label="Deadline limit (Days)"
                type="number"
                variant="standard"
                InputLabelProps={{
                  shrink: true
                }}
                onChange={(event) => setDeadline(event, index)}
                value={phase.phaseDeadline}
              />
            </>
          )}
          <Switch
            checked={"usePhase" in phase ? phase.usePhase : true}
            onChange={(event) => toggleUsePhase(event, index)}
          />
        </Box>
      )}
    </Draggable>
  )
}

export default PhaseCard

PhaseCard.propTypes = {
  phase: shape({
    isPermanent: bool,
    phaseId: shape({
      partner: oneOfType([string, number, object]),
      native: number
    }),
    name: string.isRequired,
    draggableId: string.isRequired,
    links: object,
    averageDays: number,
    goal: number
  }).isRequired,
  index: number.isRequired,
  setGoal: func.isRequired,
  setDeadline: func.isRequired,
  toggleUsePhase: func.isRequired
}
