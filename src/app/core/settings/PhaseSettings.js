"use client"

import { useState, useEffect } from "react"
import {
  NativeSelect,
  Box,
  Typography,
  Stack,
  CircularProgress,
  Button,
  LinearProgress,
  Tooltip,
  IconButton
} from "@mui/material"
import HelpOutlineIcon from "@mui/icons-material/HelpOutline"
import { Droppable, DragDropContext } from "@hello-pangea/dnd"
import { useProjectPhaseList, useTenant } from "@/helpers/firebaseGetMethods"
import { useQueryClient } from "@tanstack/react-query"
import { useUpdatePhaseList } from "./firebaseMethods.js/postMethods"
import { syncPhases } from "./firebaseMethods.js/getMethods"
import PhaseCard from "./PhaseCard"
import { func } from "prop-types"

import AlertSnackbar from "@/components/AlertSnackBar"

const reorder = (list, startIndex, endIndex) => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)

  return result
}

function PhaseSettings() {
  const [projectTypeArray, setProjectTypeArray] = useState([])
  const [projectTypeId, setProjectTypeId] = useState("")
  const [sortedPhases, setSortedPhases] = useState([])
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [isSyncingPhases, setIsSyncingPhases] = useState(false)

  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const { mutate: updatePhaseList, isPending: isUpdatingPhaseList } = useUpdatePhaseList()

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"])

  useEffect(() => {
    // Get the default project type ID for the current tenant
    if (tenantData?.userData?.defaultProjectType && tenantData?.userData?.tenant) {
      const defaultId = tenantData.userData.defaultProjectType[tenantData.userData.tenant]

      // Only set the project type ID if projectTypeList is loaded
      if (projectTypeList?.length > 0) {
        // Check if the default project type exists in the current tenant's project type list
        const projectTypeExists = projectTypeList.some(
          (type) => String(type.projectTypeId.native) === String(defaultId)
        )

        if (projectTypeExists) {
          // If it exists, use it
          setProjectTypeId(Number(defaultId))
        } else {
          // If it doesn't exist, use the first available project type
          setProjectTypeId(Number(projectTypeList[0].projectTypeId.native))

          // Show a message to the user
          setAlertInfo({
            open: true,
            message:
              "The previously selected project type is not available in this tenant. Using the first available project type instead.",
            severity: "info"
          })
        }
      }
    }
  }, [tenantData, isTenantLoading, projectTypeList])

  const {
    data: projectPhaseList,
    isLoading: areProjectPhasesLoading,
    isError: projectPhasesError
  } = useProjectPhaseList(projectTypeId, {
    // Don't retry on error to avoid multiple error messages
    retry: false,
    // Only run the query if we have a valid project type ID that exists in the list
    enabled:
      !!projectTypeId &&
      projectTypeList?.some((type) => String(type.projectTypeId.native) === String(projectTypeId))
  })

  useEffect(() => {
    if (projectTypeList?.length > 0 && projectPhaseList?.length > 0) {
      const modifiedPhases = projectPhaseList.map((phase) => ({
        ...phase,
        draggableId: phase.phaseId.native.toString()
      }))

      setProjectTypeArray(projectTypeList)
      setSortedPhases(modifiedPhases)
    }
  }, [projectTypeList, projectPhaseList, projectTypeId])

  useEffect(() => {
    if (projectPhasesError) {
      setAlertInfo({
        open: true,
        message: "Failed to load project phases. The project type may not be available in this tenant.",
        severity: "warning"
      })
    }
  }, [projectPhasesError])

  const selectProjectType = (e) => {
    setProjectTypeId(e.target.value)
  }

  const onDragEnd = (result) => {
    if (!result.destination) {
      return
    }

    const reorderedItems = reorder(sortedPhases, result.source.index, result.destination.index)
    setSortedPhases(reorderedItems)

    submitPhaseOrder(reorderedItems)
  }

  const toggleUsePhase = (event, index) => {
    const updatedPhases = [...sortedPhases]
    updatedPhases[index].usePhase = event.target.checked
    setSortedPhases(updatedPhases)
    // Submit changes after state update
    setTimeout(() => submitPhaseOrder(), 0)
  }

  const setGoal = (event, index) => {
    const updatedPhases = [...sortedPhases]
    updatedPhases[index].goal = Number(event.target.value)
    setSortedPhases(updatedPhases)
    // Submit changes after state update
    setTimeout(() => submitPhaseOrder(), 0)
  }

  const setDeadline = (event, index) => {
    const updatedPhases = [...sortedPhases]
    updatedPhases[index].phaseDeadline = Number(event.target.value)
    setSortedPhases(updatedPhases)
    // Submit changes after state update
    setTimeout(() => submitPhaseOrder(), 0)
  }

  const submitPhaseOrder = (phases = sortedPhases) => {
    const payload = {
      projectTypeId,
      newPhaseOrder: phases
    }
    updatePhaseList(payload, {
      onSuccess: () => {
        setAlertInfo({
          open: true,
          message: "Phase settings updated successfully",
          severity: "success"
        })
      },
      onError: (error) => {
        setAlertInfo({
          open: true,
          message: `Failed to update phase settings: ${error.message}`,
          severity: "error"
        })
      }
    })
  }

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleSyncPhases = async () => {
    if (!projectTypeId) return

    setIsSyncingPhases(true)
    try {
      const response = await syncPhases(projectTypeId)

      if (response?.items) {
        const modifiedPhases = response.items.map((phase) => ({
          ...phase,
          draggableId: phase.phaseId.native.toString()
        }))
        setSortedPhases(modifiedPhases)
        setAlertInfo({
          open: true,
          message: "Phases synced successfully",
          severity: "success"
        })
      }
    } catch (error) {
      setAlertInfo({
        open: true,
        message: `Failed to sync phases: ${error.message}`,
        severity: "error"
      })
    } finally {
      setIsSyncingPhases(false)
    }
  }

  // Show loading state with LinearProgress
  if (areProjectPhasesLoading || (!projectTypeId && !projectTypeList?.length)) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Stack direction="column" sx={{ margin: "25px 15px", height: "calc(100vh - 400px)" }}>
      <Stack direction="row" alignItems="center" sx={{ marginBottom: "30px" }}>
        <Typography variant="h4" sx={{ marginRight: "8px" }}>
          Set Phase Order
        </Typography>
        <Tooltip title="Drag and drop phases to reorder them, set goals and deadlines, and toggle phases on or off.">
          <IconButton size="small" sx={{ color: "text.secondary" }}>
            <HelpOutlineIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Stack>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          marginBottom: "30px"
        }}
      >
        <Stack>
          <Typography variant="h6">Project Type:</Typography>
          <NativeSelect
            sx={{
              width: "200px",
              margin: "0 30px 0 0"
            }}
            onChange={selectProjectType}
            value={projectTypeId}
          >
            {(projectTypeArray.length > 0 ? projectTypeArray : projectTypeList)?.map((type) => (
              <option key={type.projectTypeId.native} value={type.projectTypeId.native}>
                {type.name}
              </option>
            ))}
          </NativeSelect>
        </Stack>
        <Stack direction="row" gap={3}>
          {(isUpdatingPhaseList || isSyncingPhases) && <CircularProgress color="primary" />}
          <Button
            variant="contained"
            color="primary"
            onClick={handleSyncPhases}
            disabled={isSyncingPhases || !projectTypeId}
          >
            Sync Phases
          </Button>
        </Stack>
      </Stack>

      <Header />

      <Box>
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppable">
            {(droppableProvided) => (
              <Box
                ref={droppableProvided.innerRef}
                {...droppableProvided.droppableProps}
                sx={{
                  background: "#F9f9f9",
                  border: "1px solid #ddd",
                  borderRadius: "5px",
                  padding: "10px",
                  marginBottom: "20px",
                  width: "100%",
                  overflow: "auto",
                  maxHeight: "450px"
                }}
              >
                {sortedPhases?.length > 0 ? (
                  sortedPhases.map((phase, index) => (
                    <PhaseCard
                      key={phase.phaseId.native}
                      phase={phase}
                      index={index}
                      setGoal={setGoal}
                      setDeadline={setDeadline}
                      toggleUsePhase={toggleUsePhase}
                    />
                  ))
                ) : (
                  <Typography variant="body1" sx={{ padding: "20px", textAlign: "center" }}>
                    No phases found for this project type. Try using the &quot;Sync Phases&quot; button to
                    retrieve phases.
                  </Typography>
                )}
                {droppableProvided.placeholder}
              </Box>
            )}
          </Droppable>
        </DragDropContext>
      </Box>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default PhaseSettings

PhaseSettings.propTypes = {
  postProjectTypePhaseList: func.isRequired
}

const Header = () => {
  return (
    <Stack direction="row" spacing={2} sx={{ margin: "0 60px 10px 30px", width: "100%" }}>
      <Typography variant="h5" sx={{ flex: 7 }}>
        Phase Name
      </Typography>
      <Typography variant="h5" sx={{ flex: 5 }}>
        Phase Goal
      </Typography>
      <Typography variant="h5" sx={{ flex: 5 }}>
        Phase Deadline
      </Typography>
      <Typography variant="h5" sx={{ flex: 3 }}>
        On / Off
      </Typography>
    </Stack>
  )
}
