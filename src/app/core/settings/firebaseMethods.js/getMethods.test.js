import { httpsCallable } from "firebase/functions"
import { syncPhases } from "./getMethods"

// Mock dependencies
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => "mockedFunctions")
}))

// Mock the app import
jest.mock("../../../../app/firebase", () => "mockedFirebaseApp")

describe("syncPhases", () => {
  // Setup for all tests
  const mockSyncPhasesCaller = jest.fn().mockResolvedValue({ data: { items: [{ id: 1, name: "Phase 1" }] } })

  beforeEach(() => {
    jest.clearAllMocks()
    httpsCallable.mockReturnValue(mockSyncPhasesCaller)
  })

  test("calls the correct Firebase function with the provided projectTypeId", async () => {
    await syncPhases("123")

    expect(httpsCallable).toHaveBeenCalledWith("mockedFunctions", "syncProjectTypePhaseList")
    expect(mockSyncPhasesCaller).toHaveBeenCalledWith({ projectTypeId: "123" })
  })

  test("returns phase list data when API call succeeds", async () => {
    const result = await syncPhases("123")

    expect(result).toEqual({ items: [{ id: 1, name: "Phase 1" }] })
  })

  test("throws error when projectTypeId is not provided", async () => {
    await expect(syncPhases()).rejects.toThrow("Project Type ID is required")
  })

  test("throws error when API response is missing data", async () => {
    mockSyncPhasesCaller.mockResolvedValueOnce({ data: null })

    await expect(syncPhases("123")).rejects.toThrow("Failed to sync phases")
  })
})
