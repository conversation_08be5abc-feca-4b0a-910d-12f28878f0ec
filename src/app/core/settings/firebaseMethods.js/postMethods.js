import { useMutation } from "@tanstack/react-query"
import { httpsCallable, getFunctions } from "firebase/functions"
import app from "@/app/firebase"

const functions = getFunctions(app)

export const useUpdateTenant = () => {
  return useMutation({
    mutationFn: async (data) => {
      const updateUserCaller = httpsCallable(functions, "updateuser")
      return await updateUserCaller(data)
    },
    onError: (error) => {
      console.error("Error updating user tenant:", error)
    }
  })
}

export const useUpdatePhaseList = () => {
  return useMutation({
    mutationFn: async (data) => {
      const updatePhaseListCaller = httpsCallable(functions, "postprojecttypephaselist")
      return await updatePhaseListCaller(data)
    },
    onError: (error) => {
      console.error("Error updating phase list:", error)
    }
  })
}
