import { httpsCallable, getFunctions } from "firebase/functions"
import app from "@/app/firebase"

const functions = getFunctions(app)

export async function syncPhases(projectTypeId) {
  if (!projectTypeId) {
    throw new Error("Project Type ID is required")
  }

  const syncPhasesCaller = httpsCallable(functions, "syncProjectTypePhaseList")
  const response = await syncPhasesCaller({ projectTypeId })

  if (!response.data) {
    throw new Error("Failed to sync phases")
  }

  return response.data
}
