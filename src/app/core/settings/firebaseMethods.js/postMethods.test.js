import { renderHook } from "@testing-library/react"
import { useMutation } from "@tanstack/react-query"
import { httpsCallable } from "firebase/functions"
import { useUpdateTenant } from "./postMethods"

// Mock dependencies
jest.mock("@tanstack/react-query", () => ({
  useMutation: jest.fn()
}))

jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => "mockedFunctions")
}))

// Mock the app import
jest.mock("../../../../app/firebase", () => "mockedFirebaseApp")

describe("useUpdateTenant", () => {
  // Setup for all tests
  const mockMutationResult = {
    mutate: jest.fn(),
    isLoading: false,
    isError: false,
    error: null,
    isSuccess: false
  }

  const mockUpdateUserCaller = jest.fn().mockResolvedValue({ data: { success: true } })

  beforeEach(() => {
    jest.clearAllMocks()
    useMutation.mockImplementation((options) => {
      // Store the mutationFn for testing
      mockMutationResult.mutationFn = options.mutationFn
      mockMutationResult.onError = options.onError
      return mockMutationResult
    })
    httpsCallable.mockReturnValue(mockUpdateUserCaller)
  })

  test("returns mutation result from useMutation", () => {
    const { result } = renderHook(() => useUpdateTenant())
    expect(result.current).toBe(mockMutationResult)
  })

  test("calls useMutation with the correct parameters", () => {
    renderHook(() => useUpdateTenant())

    expect(useMutation).toHaveBeenCalledWith({
      mutationFn: expect.any(Function),
      onError: expect.any(Function)
    })
  })

  test("mutation function calls updateuser Firebase function", async () => {
    renderHook(() => useUpdateTenant())

    const testData = { name: "Test User", settings: { theme: "dark" } }
    await mockMutationResult.mutationFn(testData)

    expect(httpsCallable).toHaveBeenCalledWith("mockedFunctions", "updateuser")
    expect(mockUpdateUserCaller).toHaveBeenCalledWith(testData)
  })

  test("mutation function returns result from Firebase function", async () => {
    renderHook(() => useUpdateTenant())

    const testData = { name: "Test User" }
    const result = await mockMutationResult.mutationFn(testData)

    expect(result).toEqual({ data: { success: true } })
  })

  test("handles errors correctly", async () => {
    // Mock console.error to prevent test output pollution
    const originalConsoleError = console.error
    console.error = jest.fn()

    // Setup an error to be thrown
    const testError = new Error("Test error")
    mockUpdateUserCaller.mockRejectedValueOnce(testError)

    renderHook(() => useUpdateTenant())

    try {
      await mockMutationResult.mutationFn({})
    } catch (error) {
      // Error should be caught by the onError handler
    }

    // Now manually trigger the onError handler
    mockMutationResult.onError(testError)

    expect(console.error).toHaveBeenCalledWith("Error updating user tenant:", testError)

    // Restore console.error
    console.error = originalConsoleError
  })

  test("handles Firebase function rejection", async () => {
    // Setup the rejection
    const testError = new Error("Firebase function error")
    mockUpdateUserCaller.mockRejectedValueOnce(testError)

    renderHook(() => useUpdateTenant())

    // Expect the mutation function to reject with the same error
    await expect(mockMutationResult.mutationFn({})).rejects.toThrow(testError)
  })
})
