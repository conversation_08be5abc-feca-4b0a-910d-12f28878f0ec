import { Box, Button, Stack, TextField } from "@mui/material"
import { useState, useEffect } from "react"
import AlertSnackbar from "@/components/AlertSnackBar"

export default function Tenant({ submitTenant, tenant, setTenant }) {
  const [pat, setPat] = useState("")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleSubmitTenant = async () => {
    await submitTenant({
      tenant: tenant,
      pat: pat
    })
      .then(() => {
        setPat("")
        setAlertInfo({
          open: true,
          message: "Tenant created successfully",
          severity: "success"
        })
      })
      .catch((err) => {
        console.error(err, "error submitting tenant")
        setAlertInfo({
          open: true,
          message: `Error creating tenant: ${err.message || "Unknown error"}`,
          severity: "error"
        })
      })
  }

  return (
    <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
      <Box>
        <TextField
          id="tenant-input"
          label="Tenant"
          variant="standard"
          onChange={(event) => setTenant(event.target.value)}
          value={tenant}
        />
      </Box>
      <Box
        sx={{
          margin: "50px"
        }}
      >
        <TextField
          id="pat-input"
          label="PAT"
          variant="standard"
          onChange={(event) => setPat(event.target.value)}
          value={pat}
        />
      </Box>
      <Button variant="contained" color="primary" onClick={handleSubmitTenant}>
        Submit
      </Button>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}
