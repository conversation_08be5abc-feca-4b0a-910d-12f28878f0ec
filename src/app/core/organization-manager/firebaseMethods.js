import { httpsCallable, getFunctions } from "firebase/functions"
import { useQuery, useMutation } from "@tanstack/react-query"
import app from "@/app/firebase"

const functions = getFunctions(app)

export function useCheckFormChangeSubscriptionStatus(queryOptions) {
  return useQuery({
    queryKey: ["formChangeSubscriptionStatus"],
    queryFn: async () => {
      const checkSubscriptionCaller = httpsCallable(functions, "checkFormChangeSubscriptionStatus")
      const response = await checkSubscriptionCaller()
      
      if (!response.data) {
        throw new Error("Failed to check subscription status")
      }
      return response.data
    },
    ...queryOptions
  })
}

export function useDeleteFormChangeSubscription() {
  return useMutation({
    mutationFn: async () => {
      const deleteSubscriptionCaller = httpsCallable(functions, "deleteFormChangeSubscription")
      return await deleteSubscriptionCaller()
    },
    onError: (error) => {
      console.error("Error deleting form change subscription:", error)
    }
  })
}

export function useSetupFormChangeTracking() {
  return useMutation({
    mutationFn: async () => {
      const setupFormChangeTracking = httpsCallable(functions, "setupFormChangeTracking")
      return await setupFormChangeTracking()
    },
    onError: (error) => {
      console.error("Error setting up form change tracking:", error)
    }
  })
}
