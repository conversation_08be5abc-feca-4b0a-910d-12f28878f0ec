import { But<PERSON>, <PERSON><PERSON>, Typography, LinearProgress } from "@mui/material"
import { useState } from "react"
import { useQueryClient } from "@tanstack/react-query"
import AlertSnackbar from "@/components/AlertSnackBar"
import {
  useCheckFormChangeSubscriptionStatus,
  useDeleteFormChangeSubscription,
  useSetupFormChangeTracking
} from "./firebaseMethods"

export default function FormHistory() {
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const queryClient = useQueryClient()

  const {
    data: subscriptionStatus,
    isLoading: isCheckingSubscription,
    isError: subscriptionError
  } = useCheckFormChangeSubscriptionStatus()

  const { mutate: setupFormChangeTracking, isPending: isActivating } = useSetupFormChangeTracking()

  const { mutate: deleteSubscription, isPending: isDeleting } = useDeleteFormChangeSubscription()

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleActivateFormHistory = () => {
    setupFormChangeTracking(undefined, {
      onSuccess: () => {
        setAlertInfo({
          open: true,
          message: "Form change tracking activated successfully",
          severity: "success"
        })
        // Invalidate subscription status to refetch
        queryClient.invalidateQueries(["formChangeSubscriptionStatus"])
      },
      onError: (error) => {
        console.error("Setup failed:", error)
        setAlertInfo({
          open: true,
          message: `Error activating form history: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      }
    })
  }

  const handleDeleteSubscription = () => {
    deleteSubscription(undefined, {
      onSuccess: () => {
        setAlertInfo({
          open: true,
          message: "Form change subscription deleted successfully",
          severity: "success"
        })
        // Invalidate subscription status to refetch
        queryClient.invalidateQueries(["formChangeSubscriptionStatus"])
      },
      onError: (error) => {
        console.error("Delete failed:", error)
        setAlertInfo({
          open: true,
          message: `Error deleting subscription: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      }
    })
  }

  // Handle subscription error
  if (subscriptionError) {
    return (
      <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
        <Typography variant="h6" sx={{ marginBottom: "30px", textAlign: "center", color: "error.main" }}>
          Error Loading Subscription Status
        </Typography>
        <Typography variant="body2" sx={{ marginBottom: "40px", textAlign: "center", maxWidth: "400px" }}>
          Unable to check your form change subscription status. Please try again later.
        </Typography>
        <AlertSnackbar
          open={alertInfo.open}
          message={alertInfo.message}
          severity={alertInfo.severity}
          onClose={handleAlertClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        />
      </Stack>
    )
  }

  // Determine if subscription exists
  const hasSubscription = subscriptionStatus?.message === "Subscription is active" || false

  return (
    <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
      <Typography variant="h6" sx={{ marginBottom: "30px", textAlign: "center" }}>
        Form History Tracking
      </Typography>

      {isCheckingSubscription && (
        <LinearProgress sx={{ width: "100%", maxWidth: "400px", marginBottom: "20px" }} />
      )}

      <Typography variant="body2" sx={{ marginBottom: "40px", textAlign: "center", maxWidth: "400px" }}>
        {hasSubscription
          ? "Form change tracking is currently active for your organization. You can delete the subscription if you no longer need this feature."
          : "Enable tracking of form changes across your organization to monitor field modifications and updates."}
      </Typography>

      {hasSubscription ? (
        <Button
          variant="contained"
          color="error"
          onClick={handleDeleteSubscription}
          disabled={isDeleting || isCheckingSubscription}
        >
          {isDeleting ? "Deleting..." : "Delete Subscription"}
        </Button>
      ) : (
        <Button
          variant="contained"
          color="primary"
          onClick={handleActivateFormHistory}
          disabled={isActivating || isCheckingSubscription}
        >
          {isActivating ? "Activating..." : "Activate Form History"}
        </Button>
      )}

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}
