"use client"

import { Tabs, Tab, Stack, Card, Typography, LinearProgress } from "@mui/material"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

import Tenant from "./Tenant"
import User from "./User"
import FormHistory from "./FormHistory"

import { getFunctions, httpsCallable } from "firebase/functions"
import { useTenant } from "@/helpers/firebaseGetMethods"

export default function OrganizationManager() {
  const router = useRouter()
  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const functions = getFunctions()
  const submitTenant = httpsCallable(functions, "sumbmittenant")
  const createUser = httpsCallable(functions, "createuser")
  // USE DEV EMULATOR VV
  // connectFunctionsEmulator(functions, "127.0.0.1", 5001)

  const [selectedTab, setSelectedTab] = useState(0)
  const [tenant, setTenant] = useState("")

  // Security check - redirect non-admin users to auth page
  useEffect(() => {
    if (!isTenantLoading && tenantData) {
      if (tenantData?.userData?.ropsAdmin !== true) {
        // User is not an admin, redirect to auth page
        router.push("/auth")
      }
    }
  }, [tenantData, isTenantLoading, router])

  const selectTab = (_, newTab) => {
    setSelectedTab(newTab)
  }

  // Show loading indicator while checking admin status
  if (isTenantLoading) {
    return (
      <Stack
        justifyContent="center"
        alignItems="center"
        sx={{
          bgcolor: "#F3f3f3",
          height: "calc(100vh - 64px)"
        }}
      >
        <Typography variant="h5" sx={{ marginBottom: "20px" }}>
          Checking access permissions...
        </Typography>
        <LinearProgress sx={{ width: "50%" }} />
      </Stack>
    )
  }

  return (
    <Stack
      justifyContent="center"
      alignItems="center"
      sx={{
        bgcolor: "#F3f3f3",
        height: "calc(100vh - 64px)"
      }}
    >
      <Typography variant="h3">Organization Manager</Typography>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "16px",
          width: "75%",
          boxShadow: "4px 6px 6px #aaa",
          padding: "25px"
        }}
      >
        <Tabs value={selectedTab} onChange={selectTab}>
          <Tab label="Create Tenant" />
          <Tab label="Create User" />
          <Tab label="Form History" />
        </Tabs>
        {
          {
            0: <Tenant submitTenant={submitTenant} tenant={tenant} setTenant={setTenant} />,
            1: <User tenant={tenant} setTenant={setTenant} createUser={createUser} />,
            2: <FormHistory />
          }[selectedTab]
        }
      </Card>
    </Stack>
  )
}
