import { Box, Button, TextField, Stack } from "@mui/material"
import { useState } from "react"
import AlertSnackbar from "@/components/AlertSnackBar"

export default function User({ tenant, setTenant, createUser }) {
  const [emailAddress, setEmailAddress] = useState("")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleCreateUser = async () => {
    await createUser({
      tenant: tenant,
      newUserEmailAddress: emailAddress
    })
      .then(() => {
        setEmailAddress("")
        setAlertInfo({
          open: true,
          message: "User created successfully",
          severity: "success"
        })
      })
      .catch((err) => {
        console.error(err, "error submitting tenant")
        setAlertInfo({
          open: true,
          message: `Error creating user: ${err.message || "Unknown error"}`,
          severity: "error"
        })
      })
  }

  return (
    <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
      <Box>
        <TextField
          id="tenant-input"
          label="Tenant"
          variant="standard"
          onChange={(event) => setTenant(event.target.value)}
          value={tenant}
        />
      </Box>
      <Box
        sx={{
          margin: "50px"
        }}
      >
        <TextField
          id="email-address-input"
          label="Email Address"
          variant="standard"
          onChange={(event) => setEmailAddress(event.target.value)}
          value={emailAddress}
        />
      </Box>
      <Button variant="contained" color="primary" onClick={handleCreateUser}>
        Submit
      </Button>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}
