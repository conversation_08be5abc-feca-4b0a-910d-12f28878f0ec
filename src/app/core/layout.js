"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ThemeProvider, styled, useTheme } from "@mui/material/styles"
import Box from "@mui/material/Box"
import MuiDrawer from "@mui/material/Drawer"
import MuiAppBar from "@mui/material/AppBar"
import Toolbar from "@mui/material/Toolbar"
import List from "@mui/material/List"
import CssBaseline from "@mui/material/CssBaseline"
import Typography from "@mui/material/Typography"
import Divider from "@mui/material/Divider"
import IconButton from "@mui/material/IconButton"
import MenuIcon from "@mui/icons-material/Menu"
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft"
import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import ListItem from "@mui/material/ListItem"
import ListItemButton from "@mui/material/ListItemButton"
import ListItemIcon from "@mui/material/ListItemIcon"
import ListItemText from "@mui/material/ListItemText"
import DeveloperBoardIcon from "@mui/icons-material/DeveloperBoard"
import Link from "@mui/material/Link"
import LogoutIcon from "@mui/icons-material/Logout"
import TroubleshootIcon from "@mui/icons-material/Troubleshoot"
import SettingsIcon from "@mui/icons-material/Settings"
import PrecisionManufacturingIcon from "@mui/icons-material/PrecisionManufacturing"
import ClearAllIcon from "@mui/icons-material/ClearAll"
import SupervisorAccountIcon from "@mui/icons-material/SupervisorAccount"
import { node } from "prop-types"
/* import StorefrontIcon from "@mui/icons-material/Storefront"
import HistoryEduIcon from "@mui/icons-material/HistoryEdu" */

import { useRouter } from "next/navigation"
import { signOut, onAuthStateChanged } from "firebase/auth"
import { auth } from "../firebase"
import { useTenant } from "@/helpers/firebaseGetMethods"

import { defaultTheme } from "../mui-theme"
import { LayoutContextProvider } from "./LayoutContext"

const drawerWidth = 240

const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen
  }),
  overflowX: "hidden"
})

const closedMixin = (theme) => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  }),
  overflowX: "hidden",
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up("sm")]: {
    width: `calc(${theme.spacing(8)} + 1px)`
  }
})

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar
}))

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open"
})(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen
    })
  })
}))

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open"
})(({ theme, open }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  ...(open && {
    ...openedMixin(theme),
    "& .MuiDrawer-paper": openedMixin(theme)
  }),
  ...(!open && {
    ...closedMixin(theme),
    "& .MuiDrawer-paper": closedMixin(theme)
  })
}))

// Map menu items to their respective icons
const menuIcons = {
  Board: <DeveloperBoardIcon />,
  Analytics: <TroubleshootIcon />,
  Automations: <PrecisionManufacturingIcon />,
  "Support Requests": <ClearAllIcon />,
  "Organization Manager": <SupervisorAccountIcon /> /* ,
  "Marketing AI": <StorefrontIcon />,
  "Case History": <HistoryEduIcon /> */
}

export default function Layout({ children }) {
  const router = useRouter()
  const theme = useTheme()
  const [open, setOpen] = useState(false)
  const [currentSection, setCurrentSection] = useState("Kanban")
  const { data: tenantData } = useTenant()

  const handleDrawerOpen = () => {
    setOpen(true)
  }

  const handleDrawerClose = () => {
    setOpen(false)
  }

  const logOut = () => {
    signOut(auth)
      .then(() => {
        router.push("/auth")
      })
      .catch((err) => console.error(err, "sign out error"))
  }

  useEffect(
    function sendToLoginIfNoAuth() {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        if (!user) {
          router.push("/auth")
        }
      })
      return () => unsubscribe()
    },
    [router]
  )

  const sectionRouter = (section) => {
    const routes = {
      Analytics: "/core/analytics",
      Board: "/core/kanban",
      Automations: "/core/automations",
      "Support Requests": "/core/support-requests",
      "Organization Manager": "/core/organization-manager" /* ,
      "Marketing AI": "/core/coming-soon",
      "Case History": "/core/coming-soon" */
    }

    const route = routes[section]
    if (route) {
      router.push(route)
    }

    setCurrentSection(section)
  }

  return (
    <LayoutContextProvider>
      <Box sx={{ display: "flex" }}>
        <CssBaseline />
        <ThemeProvider theme={defaultTheme}>
          <AppBar position="fixed" open={open}>
            <Toolbar>
              <IconButton
                color="secondary"
                aria-label="open drawer"
                onClick={handleDrawerOpen}
                edge="start"
                sx={{
                  marginRight: 5,
                  ...(open && { display: "none" })
                }}
              >
                <MenuIcon />
              </IconButton>
              <div style={{ width: "50px" }}>
                <Image src="/legalware.png" width={50} height={35} alt="legalware.ai company logo" />
              </div>
              <Box
                sx={{
                  display: "flex",
                  width: "100%",
                  justifyContent: "space-between",
                  alignItems: "center"
                }}
              >
                <Typography variant="h5" color="secondary" noWrap component="div">
                  legalware.ai
                </Typography>
                <Link
                  underline="hover"
                  href="/core/support-requests/create"
                  onClick={(e) => {
                    e.preventDefault()
                    router.push("/core/support-requests/create")
                  }}
                >
                  <Typography variant="buttonSecondary">Create Ticket</Typography>
                </Link>
              </Box>
            </Toolbar>
          </AppBar>
          <Drawer variant="permanent" open={open}>
            <DrawerHeader>
              <IconButton onClick={handleDrawerClose}>
                {theme.direction === "rtl" ? <ChevronRightIcon /> : <ChevronLeftIcon />}
              </IconButton>
            </DrawerHeader>
            <Divider />
            <Box
              sx={{
                display: "flex",
                height: "100%",
                flexDirection: "column",
                justifyContent: "space-between"
              }}
            >
              <List>
                {[
                  "Board",
                  "Analytics",
                  "Automations",
                  "Support Requests"
                  /* , "Marketing AI", "Case History" */
                ].map((text) => (
                  <ListItem key={text} disablePadding sx={{ display: "block" }}>
                    <ListItemButton
                      selected={text === currentSection}
                      onClick={() => sectionRouter(text)}
                      sx={{
                        minHeight: 48,
                        justifyContent: open ? "initial" : "center",
                        px: 2.5
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: open ? 3 : "auto",
                          justifyContent: "center"
                        }}
                      >
                        {menuIcons[text] || null}
                      </ListItemIcon>
                      <ListItemText primary={text} sx={{ opacity: open ? 1 : 0 }} />
                    </ListItemButton>
                  </ListItem>
                ))}
                {tenantData?.userData?.ropsAdmin === true && (
                  <ListItem disablePadding sx={{ display: "block" }}>
                    <ListItemButton
                      selected={"Organization Manager" === currentSection}
                      onClick={() => sectionRouter("Organization Manager")}
                      sx={{
                        minHeight: 48,
                        justifyContent: open ? "initial" : "center",
                        px: 2.5
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: open ? 3 : "auto",
                          justifyContent: "center"
                        }}
                      >
                        {menuIcons["Organization Manager"]}
                      </ListItemIcon>
                      <ListItemText primary="Organization Manager" sx={{ opacity: open ? 1 : 0 }} />
                    </ListItemButton>
                  </ListItem>
                )}
              </List>
              <List>
                <ListItemButton
                  onClick={() => router.push("/core/settings")}
                  sx={{
                    minHeight: 48,
                    justifyContent: open ? "initial" : "center",
                    px: 2.5
                  }}
                >
                  <SettingsIcon
                    sx={{
                      minWidth: 0,
                      mr: open ? 3 : "auto",
                      justifyContent: "center"
                    }}
                  />
                  {open && <Typography>Settings</Typography>}
                </ListItemButton>
                <ListItemButton
                  onClick={logOut}
                  sx={{
                    minHeight: 48,
                    justifyContent: open ? "initial" : "center",
                    px: 2.5
                  }}
                >
                  <LogoutIcon
                    sx={{
                      minWidth: 0,
                      mr: open ? 3 : "auto",
                      justifyContent: "center"
                    }}
                  />
                  {open && <Typography>Log Out</Typography>}
                </ListItemButton>
              </List>
            </Box>
          </Drawer>
          <Box component="main" sx={{ flexGrow: 1, p: 3, padding: 0 }}>
            <DrawerHeader />
            <main>{children}</main>
          </Box>
        </ThemeProvider>
      </Box>
    </LayoutContextProvider>
  )
}

Layout.propTypes = {
  children: node.isRequired
}
