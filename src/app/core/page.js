"use client"

import { Backdrop, CircularProgress } from "@mui/material"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function App() {
  const router = useRouter()

  useEffect(() => {
    router.push("/core/kanban")
  }, [])

  return (
    <Backdrop sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }} open={true}>
      <CircularProgress color="inherit" />
    </Backdrop>
  )
}
