import {
  Box,
  Typo<PERSON>,
  Switch,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  LinearProgress,
  Collapse,
  IconButton,
} from "@mui/material"
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat"
import HighlightOffIcon from "@mui/icons-material/HighlightOff"
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown"
import { useState, useEffect } from "react"

import { getFunctions, httpsCallable } from "firebase/functions"

import AutomationBuilderDialog from "./AutomationBuilderDialog"
import DeleteAutomationModal from "./DeleteAutomationModal"
import EditAutomationDialog from "./EditAutomationDialog"
import SplitButton from "./SplitButtonComponenet"

function CustomAutomationTable({
  enableAutomation,
  changesMade,
  setChangesMade,
  automationEnabler,
  openAutomationBuilderDialog,
  setOpenAutomationBuilderDialog,
}) {
  const functions = getFunctions()

  const getCustomAutomationData = httpsCallable(
    functions,
    "getcustomautomationdata"
  )
  const setCustomAutomationStatus = httpsCallable(
    functions,
    "setcustomautomationstatus"
  )

  const [customAutomationArray, setCustomAutomationArray] = useState([])
  const [switchedAutomations, setSwitchedAutomations] = useState([])
  const [loadingCustomAutomations, setLoadingCustomAutomations] = useState(true)
  const [builder, setBuilder] = useState("")
  const [deleteAutomationModal, setDeleteAutomationModal] = useState(false)
  const [deleteAutomationData, setDeleteAutomationData] = useState("")
  const [editAutomationData, setEditAutomationData] = useState("")
  const [openAutomationEditorDialog, setOpenAutomationEditorDialog] =
    useState(false)

  useEffect(() => {
    const trigger = async () => {
      await getCustomAutomationData().then((data) => {
        console.log(data.data.customAutomationArray, "data")
        setCustomAutomationArray([...data.data.customAutomationArray])
        setLoadingCustomAutomations(false)
      })
    }

    if (!openAutomationEditorDialog) {
      trigger()
    }
  }, [loadingCustomAutomations, openAutomationEditorDialog])

  const customSwitchHandler = (e, index) => {
    let updatedArray = customAutomationArray
    updatedArray[index].enabled = e.target.checked

    setCustomAutomationArray([...updatedArray])
    setSwitchedAutomations([
      ...switchedAutomations,
      {
        triggerKey: updatedArray[index].triggerKey,
        targetSectionSelector: updatedArray[index].targetSectionSelector,
        enabled: updatedArray[index].enabled,
        automationType: updatedArray[index].automationType,
        triggerType: updatedArray[index].triggerType,
      },
    ])

    // setChangesMade(true)
  }

  const editAutomationHandler = (automation) => {
    setEditAutomationData(automation)
    setOpenAutomationEditorDialog(true)
  }

  const handleCloseDeleteModal = () => {
    setDeleteAutomationModal(false)
    setDeleteAutomationData("")
  }

  return (
    <Box>
      <Box
        sx={{
          width: "100%",
          paddingRight: "40px",
          display: "flex",
          justifyContent: "right",
        }}
      >
        <SplitButton
          setOpenAutomationBuilderDialog={setOpenAutomationBuilderDialog}
          setBuilder={setBuilder}
        />
      </Box>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <Typography variant="h5">Automation Type</Typography>
            </TableCell>
            <TableCell>
              <Typography variant="h5">Project Type</Typography>
            </TableCell>
            <TableCell>
              <Typography variant="h5">Trigger</Typography>
            </TableCell>
            <TableCell>
              <Typography variant="h5">Info</Typography>
            </TableCell>
            <TableCell align="right">
              {/* <Typography variant="h5">Status</Typography> */}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {customAutomationArray.map((automation, index) => (
            <TableRow key={index}>
              <TableCell>{automation.automationTypeLabel}</TableCell>
              <TableCell>{automation.projectTypeLabel}</TableCell>
              <TableCell>{automation.triggerFieldSelector}</TableCell>
              <TableCell>
                <Box style={{ display: "flex" }}>
                  <Typography>{automation.triggerSectionSelector}</Typography>
                  <TrendingFlatIcon fontSize="small" />
                  <Typography>{automation.targetSectionSelector}</Typography>
                </Box>
              </TableCell>
              <TableCell align="right">
                <Button onClick={() => editAutomationHandler(automation)}>
                  Edit
                </Button>
                <Switch
                  disabled={!enableAutomation}
                  checked={automation.enabled}
                  onChange={(e) => customSwitchHandler(e, index)}
                />
                <IconButton
                  onClick={() => {
                    setDeleteAutomationModal(true)
                    setDeleteAutomationData(automation)
                  }}
                >
                  <HighlightOffIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {loadingCustomAutomations ? <LinearProgress /> : ""}
      {switchedAutomations[0] || changesMade ? (
        <Button
          variant="contained"
          color="primary"
          sx={{ width: "15%", margin: "20px" }}
          onClick={() => {
            setCustomAutomationStatus({
              switchedAutomations: switchedAutomations,
            })
            setSwitchedAutomations([])
            setChangesMade(false)
            if (changesMade) {
              automationEnabler()
            }
          }}
        >
          Save Changes
        </Button>
      ) : (
        ""
      )}
      <AutomationBuilderDialog
        openAutomationBuilderDialog={openAutomationBuilderDialog}
        setOpenAutomationBuilderDialog={setOpenAutomationBuilderDialog}
        setLoadingCustomAutomations={setLoadingCustomAutomations}
        builder={builder}
        setBuilder={setBuilder}
      />
      <DeleteAutomationModal
        openModal={deleteAutomationModal}
        handleClose={handleCloseDeleteModal}
        deleteAutomationData={deleteAutomationData}
        setLoadingCustomAutomations={setLoadingCustomAutomations}
      />
      <EditAutomationDialog
        openAutomationEditorDialog={openAutomationEditorDialog}
        setOpenAutomationEditorDialog={setOpenAutomationEditorDialog}
        editAutomationData={editAutomationData}
        setEditAutomationData={setEditAutomationData}
      />
    </Box>
  )
}

export default CustomAutomationTable
