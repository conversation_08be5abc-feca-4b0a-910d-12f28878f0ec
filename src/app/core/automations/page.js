"use client"

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Switch,
  TableContainer,
  Paper,
  LinearProgress,
  Tabs,
  Tab
} from "@mui/material"
import { useState, useEffect } from "react"

import BuiltInAutomationTable from "./BuiltInAutomationTable"
import CustomAutomationTable from "./CustomAutomationTable"

import {
  getFunctions,
  httpsCallable,
  connectFunctionsEmulator,
} from "firebase/functions"
import { useRouter } from "next/navigation"

export default function AutomationPage() {
  const functions = getFunctions()
  const router = useRouter()

  // USE DEV EMULATOR VV
  // connectFunctionsEmulator(functions, "127.0.0.1", 5001)

  const automationEnabledChecker = httpsCallable(
    functions,
    "automationenabledchecker"
  )
  const automationSwitchHandler = httpsCallable(
    functions,
    "automationswitchhandler"
  )

  const [enableAutomation, setEnableAutomation] = useState("")
  const [enableLogPhaseChange, setEnableLogPhaseChange] = useState("")
  const [enableCreateCollectionNote, setEnableCreateCollectionNote] = useState("")
  const [enableProjectNumberIncrementor, setEnableProjectNumberIncrementor] =
    useState("")
  const [nextAssignedValue, setNextAssignedValue] = useState("00001")
  const [enableConcatProjectNumber, setEnableConcatProjectNumber] = useState("")
  const [changesMade, setChangesMade] = useState(false)
  const [loading, setLoading] = useState(true)
  const [selectedTab, setSelectedTab] = useState(0)
  const [openAutomationBuilderDialog, setOpenAutomationBuilderDialog] = useState(false)

  useEffect(() => {
    const getAutomationStatus = async () => {
      await automationEnabledChecker()
        .then((data) => {
          console.log(data, "data enabled checker")
          if (data.data) {
            const firebaseAutomationData = data.data

            setEnableAutomation(firebaseAutomationData.enabled)

            if (firebaseAutomationData.logPhaseChangeEnabled) {
              setEnableLogPhaseChange(true)
            } else {
              setEnableLogPhaseChange(false)
            }

            if (firebaseAutomationData?.createCollectionNote) {
              setEnableCreateCollectionNote(true)
            } else {
              setEnableCreateCollectionNote(false)
            }

            if (firebaseAutomationData.projectNumberIncrementorEnabled) {
              setEnableProjectNumberIncrementor(true)

              if (firebaseAutomationData.incrementorData.nextAssignedValue) {
                setNextAssignedValue(
                  firebaseAutomationData.incrementorData.nextAssignedValue
                )
              }
  
              if (firebaseAutomationData.incrementorData.concatenateTitle) {
                setEnableConcatProjectNumber(
                  firebaseAutomationData.incrementorData.concatenateTitle
                )
              }
              
            } else {
              setEnableProjectNumberIncrementor(false)
            }


          } else {
            setEnableAutomation(false)
          }
          setLoading(false)
        })
        .catch((err) =>
          console.log(err, "error getting automation enabled status")
        )
    }

    getAutomationStatus()
  }, [])

  const selectTab = (e, newTab) => {
    setSelectedTab(newTab)
  }

  const automationEnabler = async () => {
    const automationSwitchData = {
      enabled: enableAutomation,
      incrementorData: {
        concatenateTitle: enableConcatProjectNumber,
        nextAssignedValue: nextAssignedValue,
      },
      logPhaseChangeEnabled: enableLogPhaseChange,
      createCollectionNote: enableCreateCollectionNote,
      projectNumberIncrementorEnabled: enableProjectNumberIncrementor,
    }

    await automationSwitchHandler({
      automationSwitchData: automationSwitchData,
    }).then(() => setChangesMade(false))
  }

  const switchHandler = async (e, automationSwitch) => {
    switch (automationSwitch) {
      case "automationEnabled":
        setEnableAutomation(e.target.checked)
        break

      case "logPhaseChangeEnabled":
        setEnableLogPhaseChange(e.target.checked)
        break

      case "createCollectionNote":
        setEnableCreateCollectionNote(e.target.checked)
        break

      case "projectNumberIncrementorEnabled":
        setEnableProjectNumberIncrementor(e.target.checked)
        if (!e.target.checked) {
          setEnableConcatProjectNumber(false)
        }
        break

      case "concatenateTitleEnabled":
        setEnableConcatProjectNumber(e.target.checked)
        break

      case "nextAssignedValue":
        setNextAssignedValue(e.target.value)
    }

    setChangesMade(true)
  }

  return (
    <Box
      sx={{
        bgcolor: "#F3f3f3",
        display: "flex",
        padding: "200px 100px 400px",
        alignItems: "center",
        flexDirection: "column",
        height: "100%",
      }}
    >
      <TableContainer component={Paper}>
        { loading ? <LinearProgress /> : "" }
        <Box
          sx={{
            display: "flex",
            margin: "25px",
            justifyContent: "space-between"
          }}
          >
          <Box
            sx={{
              alignItems: "baseline",
              display: "flex", 
            }}
          >
            <Typography variant="h3">Enable Automations</Typography>
            <Switch
              disabled={loading}
              checked={enableAutomation}
              onChange={(e) => switchHandler(e, "automationEnabled")}
            />
          </Box>
          {
            // selectedTab === 1 ? <Button variant="contained" color="primary" onClick={() => setOpenAutomationBuilderDialog(true)}>Create Automation</Button> : ""
          }
        </Box>
        <Box sx={{ borderBottom: 1, borderColor: "divider", marginBottom: "40px" }}>
          <Tabs
            value={selectedTab}
            onChange={selectTab}
          >
            <Tab label="Built-In Automations" />
            <Tab label="Custom Automations" />
          </Tabs>
        </Box>
        {
          selectedTab === 0 ?
          <BuiltInAutomationTable 
            enableAutomation={enableAutomation}
            loading={loading}
            enableLogPhaseChange={enableLogPhaseChange}
            enableCreateCollectionNote={enableCreateCollectionNote}
            enableProjectNumberIncrementor={enableProjectNumberIncrementor}
            enableConcatProjectNumber={enableConcatProjectNumber}
            nextAssignedValue={nextAssignedValue}
            switchHandler={switchHandler}
            changesMade={changesMade}
            automationEnabler={automationEnabler}
          /> : 
          <CustomAutomationTable
            enableAutomation={enableAutomation}
            changesMade={changesMade}
            setChangesMade={setChangesMade}
            automationEnabler={automationEnabler}
            openAutomationBuilderDialog={openAutomationBuilderDialog}
            setOpenAutomationBuilderDialog={setOpenAutomationBuilderDialog}
          />
        }
      </TableContainer>
    </Box>
  )
}
