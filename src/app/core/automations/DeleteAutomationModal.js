import { Box, Typography, Modal, Button } from "@mui/material"
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat"
import { getFunctions, httpsCallable } from "firebase/functions"

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 600,
  bgcolor: "background.paper",
  border: "2px solid red",
  borderRadius: "7px",
  boxShadow: 24,
  p: 4,
}

function DeleteAutomationModal({
  openModal,
  handleClose,
  deleteAutomationData,
  setLoadingCustomAutomations,
}) {
  const functions = getFunctions()

  const deleteCustomAutomationData = httpsCallable(
    functions,
    "deletecustomautomationdata"
  )

  return (
    <Modal
      open={openModal}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style}>
        <Typography id="modal-modal-title" variant="h4" component="h2">
          Delete Automation
        </Typography>
        <Typography>
          Are you sure you want to delete this automation?
        </Typography>
        {deleteAutomationData ? (
          <Box
            style={{
              backgroundColor: "#ddd",
              borderRadius: "7px",
              padding: "10px",
              margin: "20px 0",
            }}
          >
            <Typography>{deleteAutomationData.automationTypeLabel}</Typography>
            <Box
              style={{
                display: "flex",
              }}
            >
              <Typography>
                {deleteAutomationData.triggerSectionSelector}{" "}
              </Typography>
              <TrendingFlatIcon fontSize="small" />
              <Typography>
                {deleteAutomationData.targetSectionSelector}
              </Typography>
            </Box>
          </Box>
        ) : (
          ""
        )}
        <Box
          style={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Button onClick={() => handleClose()}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              deleteCustomAutomationData(deleteAutomationData)
                .then(() => setLoadingCustomAutomations(true))
              handleClose()
            }}
          >
            Delete
          </Button>
        </Box>
      </Box>
    </Modal>
  )
}

export default DeleteAutomationModal
