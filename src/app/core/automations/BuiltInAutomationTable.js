import {
  Box,
  Typography,
  Switch,
  Button,
  Text<PERSON>ield,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Collapse
} from "@mui/material"

function BuiltInAutomationTable({
  enableAutomation,
  loading,
  enableLogPhaseChange,
  enableCreateCollectionNote,
  enableProjectNumberIncrementor,
  enableConcatProjectNumber,
  nextAssignedValue,
  switchHandler,
  changesMade,
  automationEnabler
}) {
  return (
    <Box>
      <Table sx={{ padding: "1000px" }}>
        <TableHead>
          <TableRow>
            <TableCell>
              <Typography variant="h5">Automation Name</Typography>
            </TableCell>
            <TableCell align="right">
              <Typography variant="h5">Active</Typography>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>Log Phase Changes to Activity Feed</TableCell>
            <TableCell align="right">
              <Switch
                disabled={!enableAutomation || loading}
                checked={enableLogPhaseChange}
                onChange={(e) => switchHandler(e, "logPhaseChangeEnabled")}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Log Collection Item Creation to Activity Feed</TableCell>
            <TableCell align="right">
              <Switch
                disabled={!enableAutomation || loading}
                checked={enableCreateCollectionNote}
                onChange={(e) => switchHandler(e, "createCollectionNote")}
              />
            </TableCell>
          </TableRow>
          <TableRow sx={{ "& > *": { borderBottom: "unset" } }}>
            <TableCell>Increment and Assign Project Number</TableCell>
            <TableCell align="right">
              <Switch
                disabled={!enableAutomation || loading}
                checked={enableProjectNumberIncrementor}
                onChange={(e) => switchHandler(e, "projectNumberIncrementorEnabled")}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={2}>
              <Collapse in={enableProjectNumberIncrementor} timeout="auto" unmountOnExit>
                <Box sx={{ padding: "10px 0 30px" }}>
                  <TextField
                    id="outlined-required"
                    label="Next Assigned Value"
                    defaultValue={nextAssignedValue}
                    onChange={(e) => switchHandler(e, "nextAssignedValue")}
                  />
                </Box>
              </Collapse>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              Concatenate Project Number on Project Name (e.g. &ldquo;Arya Stark | {nextAssignedValue}&rdquo;)
            </TableCell>
            <TableCell align="right">
              <Switch
                disabled={!enableProjectNumberIncrementor || loading}
                checked={enableConcatProjectNumber}
                onChange={(e) => switchHandler(e, "concatenateTitleEnabled")}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      {changesMade ? (
        <Button
          variant="contained"
          color="primary"
          sx={{ width: "15%", margin: "20px" }}
          onClick={automationEnabler}
        >
          Save Changes
        </Button>
      ) : (
        ""
      )}
    </Box>
  )
}

export default BuiltInAutomationTable
