import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Slide,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material"
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat"
import ArrowBackIcon from "@mui/icons-material/ArrowBack"

import { useState, useEffect, forwardRef } from "react"
import { getFunctions, httpsCallable } from "firebase/functions"

function CreateCollectionBuilder({
  setOpenAutomationBuilderDialog,
  setLoadingCustomAutomations,
  setBuilder,
  builder,
}) {
  const functions = getFunctions()

  const getProjectTypeList = httpsCallable(functions, "getprojecttypelist")
  const getAutomationSectionFieldList = httpsCallable(
    functions,
    "getautomationsectionsfieldlist"
  )
  const getProjectTypeSectionList = httpsCallable(
    functions,
    "getprojecttypesectionlist"
  )
  const createCollectionMap = httpsCallable(functions, "createcollectionmap")

  const [currentProjectTypeId, setCurrentProjectTypeId] = useState("")
  const [currentProjectTypeLabel, setCurrentProjectTypeLabel] = useState("")
  const [projectTypeArray, setProjectTypeArray] = useState([])
  const [triggerSection, setTriggerSection] = useState("")
  const [triggerSectionArray, setTriggerSectionArray] = useState([])
  const [targetSection, setTargetSection] = useState("")
  const [targetSectionArray, setTargetSectionArray] = useState([])
  const [currentActionButton, setCurrentActionButton] = useState("")
  const [actionButtonArray, setActionButtonArray] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [noActionButtonFound, setNoActionButtonFound] = useState(false)
  const [triggerValidFieldList, setTriggerValidFieldList] = useState([])
  const [targetValidFieldObject, setTargetValidFieldObject] = useState([])
  const [fieldMap, setFieldMap] = useState({})
  const [mapChangesMade, setMapChangesMade] = useState(false)

  useEffect(() => {
    const triggerGetProjectTypeList = async () => {
      await getProjectTypeList()
        .then((res) => {
          console.log(res, "returned callable data res")
          setProjectTypeArray(res.data.items)
          // setCurrentProjectTypeId( { id: res.data.items[0].projectTypeId.native, name: res.data.items[0].name } )
          setIsLoading(false)
        })
        .catch((error) => console.log(error, "returned callable data error"))
    }

    triggerGetProjectTypeList()
  }, [])

  const submitCollectionMap = async () => {
    const triggerKey = `${currentProjectTypeId}-${currentActionButton}-${triggerSection}`

    const map = {
      triggerKey: triggerKey,
      automationData: {
        enabled: true,
        fieldMap: fieldMap,
        targetSectionSelector: targetSection,
        triggerSectionSelector: triggerSection,
        triggerFieldSelector: currentActionButton,
        automationTypeLabel: `Create Collection Item From ${
          builder === "Static to Collection" ? "Static" : "Collection"
        } Section`,
        automationType: "createCollectionAutomations",
        triggerKey: triggerKey,
        triggerType: "customTaskflowAutomations",
        projectTypeLabel: currentProjectTypeLabel,
        projectTypeId: currentProjectTypeId,
      },
    }

    console.log(map, "map")

    await createCollectionMap(map)
    setOpenAutomationBuilderDialog(false)
    setLoadingCustomAutomations(true)
    setBuilder("")
    setMapChangesMade(false)
  }

  const selectTargetField = (e, triggerField) => {
    const currentMap = { ...fieldMap }
    currentMap[triggerField] = e.target.value

    setFieldMap(currentMap)
    setMapChangesMade(true)
  }

  const selectProjectType = async (e) => {
    console.log(e.target.value.projectTypeId.native, "event data")
    console.log(e.target.value.name, "event data")

    // return

    setIsLoading(true)
    setCurrentProjectTypeId(e.target.value.projectTypeId.native)
    setCurrentProjectTypeLabel(e.target.value.name)

    await getProjectTypeSectionList({
      projectTypeId: e.target.value.projectTypeId.native,
    }).then((data) => {
      console.log(data, "section data")

      if (builder === "Static to Collection") {
        setTriggerSectionArray(data.data.staticSectionList)
      }
      if (builder === "Collection to Collection") {
        setTriggerSectionArray(data.data.collectionSectionList)
      }
      setTargetSectionArray(data.data.collectionSectionList)
      setIsLoading(false)
      setFieldMap({})
    })
  }

  const selectSection = async () => {
    console.log(triggerSection, "trigger section")
    console.log(targetSection, "target section")

    setIsLoading(true)

    await getAutomationSectionFieldList({
      projectTypeId: currentProjectTypeId,
      triggerSectionSelector: triggerSection,
      targetSectionSelector: targetSection,
    }).then((data) => {
      console.log(data, "section data <--------")
      setTargetValidFieldObject(data.data.targetFieldTypeObject)
      setActionButtonArray(data.data.actionButtonList)
      if (!data.data.actionButtonList.length) {
        setNoActionButtonFound(true)
      } else {
        setNoActionButtonFound(false)
      }
      setTriggerValidFieldList(data.data.triggerValidFieldList)
      setIsLoading(false)
    })
  }

  const selectActionButton = async (e) => {
    setCurrentActionButton(e.target.value)
    console.log(e.target.key, "value")
  }

  return (
    <Box
      sx={{
        margin: "70px 200px",
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Box
        sx={{
          width: "100%",
          display: "flex",
          justifyContent: "left",
        }}
      >
        <Button
          onClick={() => {
            setBuilder("")
            setMapChangesMade(false)
          }}
        >
          <ArrowBackIcon />
        </Button>
      </Box>
      <Box sx={{ marginBottom: "50px" }}>
        <Typography variant="h3">
          Create Collection Item From{" "}
          {builder === "Static to Collection" ? "Static" : "Collection"} Section
          on Taskflow Trigger
        </Typography>
      </Box>
      <FormControl disabled={actionButtonArray[0] ? true : false}>
        <InputLabel id="project-type-select">Select Project Type</InputLabel>
        <Select
          labelId="project-type-select"
          sx={{ width: 400 }}
          onChange={selectProjectType}
          defaultValue={""}
          label="Select Project Type"
        >
          {projectTypeArray.map((type, i) => (
            <MenuItem key={i} value={type}>
              {type.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {triggerSectionArray[0] ? (
        <FormControl
          disabled={actionButtonArray[0] ? true : false}
          sx={{
            marginTop: "50px",
          }}
        >
          <InputLabel id="static-section-select">
            Select Trigger Section
          </InputLabel>
          <Select
            labelId="static-section-select"
            sx={{ width: 400 }}
            onChange={(e) => setTriggerSection(e.target.value)}
            value={triggerSection}
            label="Select Static Section"
          >
            {triggerSectionArray.map((section, i) => (
              <MenuItem key={i} value={section.sectionSelector}>
                {section.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      ) : (
        ""
      )}
      {targetSectionArray[0] ? (
        <FormControl
          disabled={actionButtonArray[0] ? true : false}
          sx={{
            marginTop: "50px",
          }}
        >
          <InputLabel id="trigger-select">Select Target Section</InputLabel>
          <Select
            labelId="trigger-select"
            sx={{ width: 400 }}
            onChange={(e) => setTargetSection(e.target.value)}
            value={targetSection}
            label="Select Collection Section"
          >
            {targetSectionArray.map((section, i) => (
              <MenuItem key={i} value={section.sectionSelector}>
                {section.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      ) : (
        ""
      )}
      {triggerSection && targetSection ? (
        <Button
          variant="contained"
          color="primary"
          onClick={() => selectSection()}
          sx={{ marginTop: "30px" }}
        >
          Get Fields
        </Button>
      ) : (
        ""
      )}
      {actionButtonArray[0] ? (
        <FormControl
          sx={{
            marginTop: "30px",
          }}
        >
          <InputLabel id="trigger-select">
            Select Trigger - Must Be Taskflow Button
          </InputLabel>
          <Select
            labelId="trigger-select"
            sx={{ width: 400 }}
            onChange={selectActionButton}
            value={currentActionButton}
            label="Select Trigger - Must Be Taskflow Button"
          >
            {actionButtonArray.map((button, i) => (
              <MenuItem key={i} value={button.fieldSelector}>
                {button.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      ) : (
        ""
      )}
      {noActionButtonFound ? (
        <Typography sx={{ marginTop: "30px" }}>
          No Taskflow Button Found in Trigger Section
        </Typography>
      ) : (
        ""
      )}
          {currentActionButton && mapChangesMade ? (
            <Box
              sx={{
                width: "100%",
                marginTop: "50px",
                paddingRight: "20px",
                display: "flex",
                justifyContent: "right",
              }}
            >
              <Button
                variant="contained"
                color="primary"
                onClick={submitCollectionMap}
              >
                Save Collection Map
              </Button>
            </Box>
          ) : (
            ""
          )}
      {currentActionButton ? (
        <Box
          sx={{
            background: "#F9f9f9",
            border: "1px solid #ddd",
            borderRadius: "5px",
            padding: "10px",
            marginTop: "30px",
            width: "100%",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <Typography variant="h5">Trigger Field Name</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="h5">Trigger Field Type</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="h5"></Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="h5">Mappable Target Fields</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {triggerValidFieldList.map((triggerField) => (
                  <TableRow key={triggerField.fieldSelector}>
                    <TableCell>{triggerField.name}</TableCell>
                    <TableCell>{triggerField.customFieldType}</TableCell>
                    <TableCell>
                      {targetValidFieldObject[
                        triggerField.customFieldType
                      ][0] ? (
                        <TrendingFlatIcon />
                      ) : (
                        ""
                      )}
                    </TableCell>
                    <TableCell align="right">
                      {targetValidFieldObject[
                        triggerField.customFieldType
                      ][0] ? (
                        <FormControl>
                          <InputLabel id="target-select">
                            Select Target Field
                          </InputLabel>
                          <Select
                            labelId="target-select"
                            sx={{ width: 300 }}
                            onChange={(e) =>
                              selectTargetField(e, triggerField.fieldSelector)
                            }
                            value={
                              fieldMap[triggerField.fieldSelector]
                                ? fieldMap[triggerField.fieldSelector]
                                : ""
                            }
                            label="Select Target Field"
                          >
                            {fieldMap[triggerField.fieldSelector] ? (
                              <MenuItem value={null}>
                                <em>None</em>
                              </MenuItem>
                            ) : (
                              ""
                            )}
                            {targetValidFieldObject[
                              triggerField.customFieldType
                            ].map((collectionField, index) => (
                              <MenuItem
                                key={index}
                                value={collectionField.fieldSelector}
                              >
                                {collectionField.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      ) : (
                        <Typography>No Valid Target Fields</Typography>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
        </Box>
      ) : (
        ""
      )}
      {isLoading ? <CircularProgress sx={{ marginTop: "20px" }} /> : ""}
      {currentActionButton && mapChangesMade ? (
        <Box
          sx={{
            width: "100%",
            marginTop: "20px",
            paddingRight: "20px",
            display: "flex",
            justifyContent: "right",
          }}
        >
          <Button
            variant="contained"
            color="primary"
            onClick={submitCollectionMap}
          >
            Save Collection Map
          </Button>
        </Box>
      ) : (
        ""
      )}
    </Box>
  )
}

export default CreateCollectionBuilder
