import {
  A<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  Divider,
  IconButton,
  Slide,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Toolbar,
  Typography,
} from "@mui/material"
import CloseIcon from "@mui/icons-material/Close"

import { useState, forwardRef } from "react"

import CreateCollectionBuilder from "./CreateCollectionBuilder"

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />
})

function CreateCollectionModal({
  openAutomationBuilderDialog,
  setOpenAutomationBuilderDialog,
  setLoadingCustomAutomations,
  builder,
  setBuilder
}) {
  

  return (
    <Dialog
      fullScreen
      open={openAutomationBuilderDialog}
      onClose={() => {
        setBuilder("")  
        setOpenAutomationBuilderDialog(false)
      }}
      TransitionComponent={Transition}
    >
      <AppBar sx={{ position: "relative" }}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => {
              setBuilder("")
              setOpenAutomationBuilderDialog(false)
            }}
          >
            <CloseIcon />
          </IconButton>
          <Typography variant="h3">Automation Builder</Typography>
        </Toolbar>
      </AppBar>
      {builder === "" ? (
        <Box
          sx={{
            margin: "100px 200px",
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell width={500}><Typography variant="h4">Description</Typography></TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell>
                  <Typography>When automation is triggered within a Static Section using a Taskflow Action Button, a new Collection Item will be created and qualifying data can be copied</Typography>
                </TableCell>
                <TableCell align="right">
                  <Button 
                    onClick={() => setBuilder("Static to Collection")}
                    variant="contained"  
                    >
                    Create Collection Item from Static Section
                  </Button>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <Typography>When automation is triggered within a Collection Item using a Taskflow Action Button, a Collection Item will be created and qualifying data can be copied</Typography>
                </TableCell>
                <TableCell align="right">
                  <Button 
                    onClick={() => setBuilder("Collection to Collection")}
                    variant="contained"  
                  >
                    Create Collection Item from Collection Section
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Box>
      ) : (
        ""
      )}
      {builder === "Static to Collection" || builder === "Collection to Collection" ? (
        <CreateCollectionBuilder
          setOpenAutomationBuilderDialog={setOpenAutomationBuilderDialog}
          setLoadingCustomAutomations={setLoadingCustomAutomations}
          setBuilder={setBuilder}
          builder={builder}
        />
      ) : (
        ""
      )}
    </Dialog>
  )
}

export default CreateCollectionModal
