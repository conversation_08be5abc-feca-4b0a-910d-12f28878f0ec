import { node } from "prop-types"
import { createContext, useContext, useMemo } from "react"

const LayoutContext = createContext(undefined)

export const LayoutContextProvider = ({ children }) => {
  const value = useMemo(() => ({}), [])

  return <LayoutContext.Provider value={value}>{children}</LayoutContext.Provider>
}
LayoutContextProvider.propTypes = {
  children: node.isRequired
}

export const useLayoutContext = () => {
  const context = useContext(LayoutContext)

  if (context === undefined) {
    throw new Error("useLayoutContext must be used within a LayoutContextProvider")
  }

  return context
}
