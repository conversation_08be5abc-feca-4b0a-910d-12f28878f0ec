"use client"

import { useState } from "react"
import { useTenant } from "@/helpers/firebaseGetMethods"
import { useSupportRequests } from "./firebaseMethods"
import {
  Stack,
  Typography,
  Card,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  LinearProgress,
  Chip,
  TableSortLabel
} from "@mui/material"
// import { useSupportRequestsContext } from "./SupportRequestsContext"

function formatDate(dateString) {
  if (!dateString) return ""

  const date = new Date(dateString)

  // Check if the date is valid
  if (isNaN(date.getTime())) return dateString

  // Format options for the date - only showing date (no time)
  const options = {
    year: "numeric",
    month: "long",
    day: "numeric"
  }

  return date.toLocaleDateString("en-US", options)
}

// Status pill styles
const STATUS_STYLES = {
  New: {
    bgcolor: "#FFC107", // Yellow
    color: "#000000"
  },
  "In Progress": {
    bgcolor: "#2196F3", // Blue
    color: "#FFFFFF"
  },
  Completed: {
    bgcolor: "#4CAF50", // Green
    color: "#FFFFFF"
  }
}

// Function to get status style
function getStatusStyle(status) {
  return STATUS_STYLES[status] || { bgcolor: "#E0E0E0", color: "#000000" } // Default gray
}

// Function to compare values for sorting
function descendingComparator(a, b, orderBy) {
  // Special case for date comparison
  if (orderBy === "createdDate") {
    return new Date(b.createdDate) - new Date(a.createdDate)
  }

  // Special case for status in dataObject
  if (orderBy === "status") {
    return (b.dataObject?.status || "").localeCompare(a.dataObject?.status || "")
  }

  // Special case for system in dataObject
  if (orderBy === "system") {
    return (b.dataObject?.system || "").localeCompare(a.dataObject?.system || "")
  }

  // Special case for assignedTo in dataObject
  if (orderBy === "assignedTo") {
    return (b.dataObject?.assignedTo?.fullname || "").localeCompare(a.dataObject?.assignedTo?.fullname || "")
  }

  // Default comparison
  if (b[orderBy] < a[orderBy]) {
    return -1
  }
  if (b[orderBy] > a[orderBy]) {
    return 1
  }
  return 0
}

function getComparator(order, orderBy) {
  return order === "desc"
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy)
}

export default function SupportRequests() {
  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const { data: supportRequests, isLoading: supportRequestLoading } = useSupportRequests(
    tenantData?.userData.tenant
  )

  // Sorting state
  const [order, setOrder] = useState("desc")
  const [orderBy, setOrderBy] = useState("createdDate")

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc"
    setOrder(isAsc ? "desc" : "asc")
    setOrderBy(property)
  }

  const loading = isTenantLoading || supportRequestLoading

  return (
    <Stack
      direction="column"
      alignContent="center"
      flexWrap="wrap"
      sx={{
        bgcolor: "#F3f3f3",
        paddingTop: "100px",
        height: "calc(100vh - 64px)"
      }}
    >
      <Typography variant="h3">Support Requests</Typography>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "16px",
          width: "85%",
          boxShadow: "4px 6px 6px #aaa",
          padding: "25px"
        }}
      >
        <TableContainer
          component={Paper}
          sx={{
            maxHeight: "calc(100vh - 350px)",
            overflow: "auto"
          }}
        >
          {loading && <LinearProgress />}
          <Table>
            <TableHeader order={order} orderBy={orderBy} onRequestSort={handleRequestSort} />
            <TableBody>
              {supportRequests?.projectCollectionItemList &&
                [...supportRequests.projectCollectionItemList]
                  .sort(getComparator(order, orderBy))
                  .map((request) => (
                    <TableRow key={request.itemId.native}>
                      <TableCell>{request.dataObject?.system}</TableCell>
                      <TableCell>
                        <Chip
                          label={request.dataObject?.status}
                          size="small"
                          sx={getStatusStyle(request.dataObject?.status)}
                        />
                      </TableCell>
                      <TableCell>{formatDate(request.createdDate)}</TableCell>
                      <TableCell>{request.dataObject?.assignedTo?.fullname}</TableCell>
                      <TableCell>{request.dataObject?.description}</TableCell>
                    </TableRow>
                  ))}
              {supportRequests?.projectCollectionItemList?.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No support requests found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>
    </Stack>
  )
}

function TableHeader({ order, orderBy, onRequestSort }) {
  const COLUMNS = [
    { id: "system", label: "System", sortable: true },
    { id: "status", label: "Status", sortable: true },
    { id: "createdDate", label: "Date of creation", sortable: true },
    { id: "assignedTo", label: "Assigned to", sortable: true },
    { id: "description", label: "Description", sortable: false }
  ]

  const createSortHandler = (property) => () => {
    if (property !== "description") {
      onRequestSort(property)
    }
  }

  return (
    <TableHead>
      <TableRow>
        {COLUMNS.map((column) => (
          <TableCell key={column.id}>
            {column.sortable ? (
              <TableSortLabel
                active={orderBy === column.id}
                direction={orderBy === column.id ? order : "asc"}
                onClick={createSortHandler(column.id)}
              >
                <Typography variant="h6">{column.label}</Typography>
              </TableSortLabel>
            ) : (
              <Typography variant="h6">{column.label}</Typography>
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}
