import { httpsCallable, getFunctions } from "firebase/functions"
import { useQuery, useMutation } from "@tanstack/react-query"
import app from "@/app/firebase"

const functions = getFunctions(app)

export function useSupportRequests(tenant, queryOptions) {
  return useQuery({
    queryKey: ["supportRequests"],
    queryFn: async () => {
      const supportRequestsCaller = httpsCallable(functions, "getSupportTickets")
      const response = await supportRequestsCaller({ tenant })
      if (!response.data) {
        throw new Error("Failed to fetch support requests")
      }
      return response.data
    },
    enabled: !!tenant,
    ...queryOptions
  })
}

export function useSupportProjectPrimary(tenant, queryOptions) {
  return useQuery({
    queryKey: ["supportRequests"],
    queryFn: async () => {
      const supportRequestsCaller = httpsCallable(functions, "getSupportProjectPrimary")
      const response = await supportRequestsCaller({ tenant })
      if (!response.data) {
        throw new Error("Failed to fetch support requests")
      }
      return response.data
    },
    enabled: !!tenant,
    ...queryOptions
  })
}

export function useCreateSupportRequest() {
  return useMutation({
    mutationFn: async (tenant, dataobject) => {
      const createSupportRequestCaller = httpsCallable(functions, "createSupportTicket")
      return await createSupportRequestCaller(tenant, dataobject)
    },
    onError: (error) => {
      console.error("Error creating support request:", error)
    }
  })
}
