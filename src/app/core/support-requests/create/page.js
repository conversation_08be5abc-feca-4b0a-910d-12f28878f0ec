"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { auth } from "@/app/firebase"
import {
  Stack,
  Typography,
  Card,
  TextField,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Divider,
  Box,
  LinearProgress
} from "@mui/material"
import { useTenant } from "@/helpers/firebaseGetMethods"
import { useSupportProjectPrimary, useCreateSupportRequest } from "../firebaseMethods"
import AlertSnackbar from "@/components/AlertSnackBar"

// Systems options for dropdown
const SYSTEMS = ["Unknown", "Filevine", "FVDA", "Periscope", "Outlaw", "Lead Docket", "VineSign"]

export default function CreateSupportRequest() {
  const router = useRouter()
  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const {
    mutate: createSupportRequest,
    isPending: isCreatingRequest,
    isError: isCreatingRequestError
  } = useCreateSupportRequest()
  const { data: supportProjectPrimary, isLoading: supportProjectPrimaryLoading } = useSupportProjectPrimary(
    tenantData?.userData.tenant
  )
  const loading = isTenantLoading || supportProjectPrimaryLoading || isCreatingRequest

  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [formData, setFormData] = useState({
    createdBy: "",
    system: "",
    description: "",
    createDate: new Date().toISOString(),
    status: "New",
    assignedTo: { id: "" }
  })

  // Redirect if tenant is ropsio
  useEffect(() => {
    if (!loading && tenantData?.userData && auth && supportProjectPrimary?.personId?.native) {
      setFormData((prevData) => ({
        ...prevData,
        createdBy: auth?.currentUser?.email || "",
        assignedTo: { id: supportProjectPrimary.personId?.native || "" }
      }))
    }
  }, [isTenantLoading, tenantData, router, loading, supportProjectPrimary])

  useEffect(() => {
    if (isCreatingRequestError) {
      setAlertInfo({
        open: true,
        message: "Failed to create support request. Please try again later.",
        severity: "error"
      })
    }
  }, [isCreatingRequestError])

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prevData) => ({
      ...prevData,
      [name]: value
    }))
  }

  // Handle alert close
  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    createSupportRequest(
      { tenant: tenantData?.userData.tenant, dataobject: formData },
      {
        onSuccess: () => {
          setAlertInfo({
            open: true,
            message: "Support request created successfully, redirecting...",
            severity: "success"
          })
          setTimeout(() => {
            router.push("/core/support-requests")
          }, 1500)
        }
      }
    )
  }

  if (isTenantLoading || supportProjectPrimaryLoading) {
    return (
      <Stack
        direction="column"
        alignItems="center"
        justifyContent="center"
        sx={{
          bgcolor: "#F3f3f3",
          height: "calc(100vh - 64px)"
        }}
      >
        <LinearProgress sx={{ width: "50%", mb: 2 }} />
        <Typography>Loading...</Typography>
      </Stack>
    )
  }

  return (
    <Stack
      direction="column"
      alignItems="center"
      sx={{
        bgcolor: "#F3f3f3",
        paddingTop: "100px",
        minHeight: "calc(100vh - 64px)"
      }}
    >
      <Typography variant="h3">Create Support Request</Typography>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "16px",
          width: "85%",
          maxWidth: "800px",
          boxShadow: "4px 6px 6px #aaa",
          padding: "25px"
        }}
      >
        {isCreatingRequest && <LinearProgress color="primary" sx={{ marginBottom: "16px" }} />}

        <form onSubmit={handleSubmit}>
          <Stack spacing={3}>
            <FormControl fullWidth required>
              <InputLabel id="system-label">System</InputLabel>
              <Select
                labelId="system-label"
                id="system"
                name="system"
                value={formData.system}
                label="System"
                onChange={handleChange}
                disabled={loading}
              >
                {SYSTEMS.map((system) => (
                  <MenuItem key={system} value={system}>
                    {system}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              id="description"
              name="description"
              label="Description"
              multiline
              rows={12}
              value={formData.description}
              onChange={handleChange}
              fullWidth
              required
              disabled={loading}
              variant="outlined"
            />

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Button
                variant="outlined"
                onClick={() => router.push("/core/support-requests")}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={!formData.system || !formData.description || loading}
              >
                Submit Request
              </Button>
            </Box>
          </Stack>
        </form>
      </Card>
      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
      />
    </Stack>
  )
}
