import { createContext, useContext, useMemo, useState } from "react"
import { node } from "prop-types"

const SupportRequestsContext = createContext(undefined)

export const SupportRequestsContextProvider = ({ children }) => {
  const value = useMemo(() => ({}), [])

  return <SupportRequestsContext.Provider value={value}>{children}</SupportRequestsContext.Provider>
}
SupportRequestsContextProvider.propTypes = {
  children: node.isRequired
}

export const useSupportRequestsContext = () => {
  const context = useContext(SupportRequestsContext)

  if (context === undefined) {
    throw new Error("useSupportRequestsContext must be used within a SupportRequestsContextProvider")
  }

  return context
}
