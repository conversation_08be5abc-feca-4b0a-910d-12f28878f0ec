import { Droppable } from "@hello-pangea/dnd"
import { Box } from "@mui/material"
import { arrayOf, shape, string, number, oneOfType, func, object, bool } from "prop-types"
import ColumnHeader from "./ColumnHeader"
import ProjectCard from "./ProjectCard"
import getPhaseBgColor from "./helpers/getPhaseBgColor"

function PhaseColumn({ phase, projects, createNote }) {
  return (
    <Box
      sx={{
        flex: 1,
        minWidth: 210,
        bgcolor: getPhaseBgColor(phase)
      }}
    >
      <ColumnHeader phase={phase} projects={projects} />
      <Droppable droppableId={String(phase.phaseId.native)}>
        {(droppableProvided, snapshot) => (
          <Box
            ref={droppableProvided.innerRef}
            {...droppableProvided.droppableProps}
            className={snapshot.isDraggingOver ? "isDraggingOver" : ""}
            sx={{
              display: "flex",
              flexDirection: "column",
              borderRadius: 2,
              padding: "0px",
              "&.isDraggingOver": {
                bgcolor: "#EBEBEB"
              }
            }}
          >
            {projects?.projectArray.length > 0 && (
              <>
                {projects?.projectArray.map((project, index) => (
                  <ProjectCard
                    key={project.projectId.native}
                    project={project}
                    position={index}
                    createNote={createNote}
                  />
                ))}
              </>
            )}
            {droppableProvided.placeholder}
          </Box>
        )}
      </Droppable>
    </Box>
  )
}
export default PhaseColumn

PhaseColumn.propTypes = {
  phase: shape({
    averageDays: number,
    draggableId: string,
    goal: number,
    isPermanent: bool,
    links: object,
    name: string,
    phaseId: shape({ partner: oneOfType([string, number]), native: number })
  }).isRequired,
  projects: shape({
    projectArray: arrayOf(
      shape({
        links: shape({
          self: string,
          projectType: string,
          rootFolder: string,
          client: string,
          contacts: string
        }),
        phaseName: string,
        clientName: string,
        firstPrimaryUsername: string,
        firstPrimaryName: string,
        hashtags: arrayOf(string),
        projectEmailAddress: string,
        createdDate: string,
        projectId: shape({
          native: number,
          partner: oneOfType([string, number])
        }),
        projectTypeId: shape({
          native: number,
          partner: oneOfType([string, number])
        }),
        projectName: string,
        phaseId: shape({
          native: number,
          partner: oneOfType([string, number])
        }),
        number: string,
        projectUrl: string,
        daysSpentInPhase: number,
        formattedDate: string,
        index: number
      })
    )
  }).isRequired,
  createNote: func.isRequired
}
