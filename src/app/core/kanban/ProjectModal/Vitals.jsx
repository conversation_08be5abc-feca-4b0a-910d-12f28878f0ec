import { number, arrayOf, string, shape, object, bool, func } from "prop-types"
import { Box, Typography, LinearProgress, IconButton, Stack, Divider, <PERSON> } from "@mui/material"
import FilevineLogo from "@/svgComponents/FilevineLogo"
import LaunchIcon from "@mui/icons-material/Launch"
import Details from "./Details"
import ReplayIcon from "@mui/icons-material/Replay"
import { dateValidator } from "../helpers/DateValidator"

const Vitals = ({ project, vitals, loading, error, refetch }) => {
  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center" margin="0 0 32px 0">
        <Typography variant="h4" style={{ marginTop: "10px" }}>
          Project Details
        </Typography>
        <Link href={project?.projectUrl} target="_blank" rel="noreferrer">
          <Stack direction="row" alignItems="center">
            <FilevineLogo />
            <LaunchIcon fontSize="medium" sx={{ margin: "6px 0 0 8px" }} />
          </Stack>
        </Link>
      </Stack>

      <Details project={project} />
      <Divider sx={{ margin: "25px 0" }} />
      <Typography variant="h4" sx={{ margin: "10px 25px 25px 0" }}>
        Project Vitals
      </Typography>
      {vitals?.map((vital) => (
        <Stack
          direction="row"
          key={vital.friendlyName}
          style={{
            display: "flex",
            flexDirection: "row"
          }}
        >
          <Typography variant="h6" sx={{ flex: 1 }}>
            {vital.friendlyName}:
          </Typography>
          <Typography variant="h7" sx={{ flex: 3, margin: "1px 5px" }}>
            {dateValidator(vital.value)}
          </Typography>
        </Stack>
      ))}

      {loading && <LinearProgress color="primary" />}

      {error && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center"
          }}
        >
          <Typography sx={{ color: "red", marginRight: "10px" }}>Error loading project vitals</Typography>
          <IconButton onClick={refetch}>
            <ReplayIcon />
          </IconButton>
        </Box>
      )}
    </>
  )
}
Vitals.propTypes = {
  project: shape({
    links: shape({
      self: string.isRequired,
      projectType: string.isRequired,
      rootFolder: string.isRequired,
      client: string.isRequired,
      contacts: string.isRequired
    }).isRequired,
    phaseName: string.isRequired,
    clientName: string.isRequired,
    firstPrimaryUsername: string.isRequired,
    firstPrimaryName: string.isRequired,
    hashtags: arrayOf(string).isRequired,
    projectEmailAddress: string.isRequired,
    createdDate: string.isRequired,
    projectId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectTypeId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectName: string.isRequired,
    phaseId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    number: string.isRequired,
    projectUrl: string.isRequired,
    daysSpentInPhase: number.isRequired,
    formattedDate: string.isRequired,
    index: number.isRequired
  }).isRequired,
  vitals: arrayOf(
    shape({
      fieldName: string,
      fieldType: string,
      friendlyName: string,
      value: string,
      position: number,
      links: object
    })
  ),
  loading: bool.isRequired,
  error: bool.isRequired,
  refetch: func.isRequired
}

export default Vitals
