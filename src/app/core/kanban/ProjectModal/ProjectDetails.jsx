import { number, arrayOf, shape, string } from "prop-types"
import { Stack, Paper, LinearProgress } from "@mui/material"
import Vitals from "./Vitals"
import PhaseLog from "./PhaseLog"
import PhasePerformance from "./PhasePerformance"
import { useQueryClient } from "@tanstack/react-query"
import { useProjectAttributes } from "@/helpers/firebaseGetMethods"
import { useKanbanContext } from "../KanbanContext"

const ProjectDetails = ({ project, selectedTab }) => {
  const { data, isLoading, isError, refetch } = useProjectAttributes(project?.projectId?.native)
  const { projectTypeId } = useKanbanContext()

  const queryClient = useQueryClient()
  const projectPhaseList = queryClient.getQueryData([
    "projectPhaseList",
    { projectTypeId: String(projectTypeId) }
  ])

  const renderContent = () => {
    if (selectedTab === 0) {
      return (
        <Vitals
          project={project}
          vitals={data.vitals}
          loading={isLoading}
          error={isError}
          refetch={refetch}
        />
      )
    }
    if (selectedTab === 1) {
      return <PhaseLog data={data.phaseHistory} />
    }
    if (selectedTab === 2) {
      return <PhasePerformance project={project} data={data.phaseHistory} phases={projectPhaseList} />
    }
    return null
  }

  return (
    <Stack width="60%">
      <Paper elevation={3} sx={{ backgroundColor: "white", padding: "25px", flex: 1 }}>
        {isLoading ? <LinearProgress color="primary" /> : renderContent()}
      </Paper>
    </Stack>
  )
}
ProjectDetails.propTypes = {
  project: shape({
    links: shape({
      self: string.isRequired,
      projectType: string.isRequired,
      rootFolder: string.isRequired,
      client: string.isRequired,
      contacts: string.isRequired
    }).isRequired,
    phaseName: string.isRequired,
    clientName: string.isRequired,
    firstPrimaryUsername: string.isRequired,
    firstPrimaryName: string.isRequired,
    hashtags: arrayOf(string).isRequired,
    projectEmailAddress: string.isRequired,
    createdDate: string.isRequired,
    projectId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectTypeId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectName: string.isRequired,
    phaseId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    number: string.isRequired,
    projectUrl: string.isRequired,
    daysSpentInPhase: number.isRequired,
    formattedDate: string.isRequired,
    index: number.isRequired
  }).isRequired,
  selectedTab: number.isRequired
}

export default ProjectDetails
