import { useState } from "react"
import { shape, string, arrayOf, number, func } from "prop-types"
import NoteList from "./NoteList"
import { Stack, Collapse, Typography, LinearProgress, Divider } from "@mui/material"
import ProjectActionsForm from "./ProjectActionsForm"
import { useProjectAttributes } from "@/helpers/firebaseGetMethods"

const ProjectActions = ({ createNote, project }) => {
  const [noteBody, setNoteBody] = useState("")
  const [submittingNote, setSubmittingNote] = useState(false)
  const [notifyUser, setNotifyUser] = useState("")
  const [taskUser, setTaskUser] = useState("")
  const [noteError, setNoteError] = useState("")

  const { data, isLoading, isFetching, refetch } = useProjectAttributes(project.projectId.native)

  const handleTaskUserSelect = (value) => {
    setTaskUser(String(value))
  }

  const handleNoteUserSelect = (value) => {
    setNotifyUser(String(value))
  }

  const postNote = async () => {
    setSubmittingNote(true)

    const bodyObj = {
      projectId: {
        native: project.projectId.native
      },
      body: notifyUser ? `+@${notifyUser} ${noteBody}` : noteBody
    }

    if (taskUser) {
      bodyObj.assigneeId = { native: taskUser }
    }

    await createNote(bodyObj)
      .then(() => {
        setSubmittingNote(false)
        setNoteBody("")
        setNotifyUser("")
        setTaskUser("")
        setNoteError("")
        refetch()
      })
      .catch((err) => {
        setNoteError(`${err}, error submitting note`)
        setSubmittingNote(false)
      })
  }

  return (
    <Stack padding="12px 0" width="40%">
      <Collapse in={!submittingNote}>
        {isLoading ? (
          <LinearProgress color="primary" sx={{ marginTop: "16px" }} />
        ) : (
          <ProjectActionsForm
            taskUser={taskUser}
            noteBody={noteBody}
            setNoteBody={setNoteBody}
            noteError={noteError}
            setNotifyUser={handleNoteUserSelect}
            notifyUser={notifyUser}
            projectTeam={data.projectTeam}
            setTaskUser={handleTaskUserSelect}
            postNote={postNote}
          />
        )}
      </Collapse>
      {submittingNote ? (
        <>
          <LinearProgress color="primary" />
          <Typography variant="subtitle1">Submitting {!taskUser ? "Note" : "Task"}...</Typography>
        </>
      ) : (
        <></>
      )}
      <Divider color="#e1b153" sx={{ margin: "35px 0" }} />
      {isFetching && <LinearProgress color="primary" sx={{ marginBottom: "16px" }} />}
      {!isLoading && <NoteList noteList={data?.projectNoteList} />}
    </Stack>
  )
}

export default ProjectActions

ProjectActions.propTypes = {
  createNote: func.isRequired,
  project: shape({
    links: shape({
      self: string.isRequired,
      projectType: string.isRequired,
      rootFolder: string.isRequired,
      client: string.isRequired,
      contacts: string.isRequired
    }).isRequired,
    phaseName: string.isRequired,
    clientName: string.isRequired,
    firstPrimaryUsername: string.isRequired,
    firstPrimaryName: string.isRequired,
    hashtags: arrayOf(string).isRequired,
    projectEmailAddress: string.isRequired,
    createdDate: string.isRequired,
    projectId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectTypeId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectName: string.isRequired,
    phaseId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    number: string.isRequired,
    projectUrl: string.isRequired,
    daysSpentInPhase: number.isRequired,
    formattedDate: string.isRequired,
    index: number.isRequired
  }).isRequired
}
