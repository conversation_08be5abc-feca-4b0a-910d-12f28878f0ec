import { render, fireEvent, screen } from "@testing-library/react"
import PhasePerformanceChart from "./PhasePerformanceChart"

const mockPhaseHistoryData = {
  highestValue: 30,
  data: [
    {
      phaseName: "Planning",
      phaseId: 1,
      timeInPhase: 15,
      average: 10,
      goal: 20
    },
    {
      phaseName: "Development",
      phaseId: 2,
      timeInPhase: 25,
      average: 20,
      goal: 25
    }
  ],
  maxTimeInPhase: 25,
  maxAverage: 20,
  maxGoal: 25
}

const noGoalData = {
  ...mockPhaseHistoryData,
  data: [
    {
      ...mockPhaseHistoryData.data[0],
      goal: null
    },
    {
      ...mockPhaseHistoryData.data[1],
      goal: null
    }
  ]
}

jest.mock("d3", () => ({
  scaleLinear: () => {
    const scale = (value) => value
    scale.domain = () => scale
    scale.range = () => scale
    return scale
  }
}))

describe("PhaseHistoryChart", () => {
  const setup = () => {
    return render(<PhasePerformanceChart phasePerformanceData={mockPhaseHistoryData} native={1} />)
  }

  it("renders correctly", () => {
    render(<PhasePerformanceChart phasePerformanceData={mockPhaseHistoryData} native={1} />)

    expect(screen.getByText("Planning")).toBeInTheDocument()
    expect(screen.getByText("Development")).toBeInTheDocument()
    expect(screen.getByText("Project:")).toBeInTheDocument()
    expect(screen.getByText("Organization:")).toBeInTheDocument()
  })

  it("handles mouse interactions correctly with goal data", () => {
    render(<PhasePerformanceChart phasePerformanceData={mockPhaseHistoryData} native={1} />)

    const phaseElement = screen.getByText("Planning").parentElement
    fireEvent.mouseEnter(phaseElement)
    expect(screen.getByText("Goal: 20 days")).toBeInTheDocument()

    // Mouse move
    fireEvent.mouseMove(phaseElement, {
      clientX: 100,
      clientY: 100
    })
    expect(screen.getByText("Goal: 20 days")).toBeInTheDocument()

    // Mouse leave
    fireEvent.mouseLeave(phaseElement)
    expect(screen.queryByText("Goal: 20 days")).not.toBeInTheDocument()
  })

  it("handles mouse interactions correctly with null goal data", () => {
    render(<PhasePerformanceChart phasePerformanceData={noGoalData} native={1} />)

    const phaseElement = screen.getByText("Planning").parentElement
    fireEvent.mouseEnter(phaseElement)

    // Check for "Goal: -" instead of "Goal: 20 days"
    expect(screen.getByText("Goal: -")).toBeInTheDocument()

    // Mouse move
    fireEvent.mouseMove(phaseElement, {
      clientX: 100,
      clientY: 100
    })
    expect(screen.getByText("Goal: -")).toBeInTheDocument()

    // Mouse leave
    fireEvent.mouseLeave(phaseElement)
    expect(screen.queryByText("Goal: -")).not.toBeInTheDocument()
  })

  it("handles scroll events", () => {
    const { container } = setup()
    const svgContainer = container.querySelector("div")

    fireEvent.scroll(svgContainer, {
      target: {
        scrollLeft: 100,
        scrollTop: 50
      }
    })
  })

  it("handles window resize", () => {
    const { container, rerender } = setup()

    // Mock offsetWidth/Height
    Object.defineProperty(container.firstChild, "offsetWidth", {
      configurable: true,
      value: 500
    })
    Object.defineProperty(container.firstChild, "offsetHeight", {
      configurable: true,
      value: 400
    })

    // Trigger resize
    window.dispatchEvent(new Event("resize"))

    // Rerender with new dimensions
    rerender(<PhasePerformanceChart phasePerformanceData={mockPhaseHistoryData} native={1} />)
  })

  it("renders tooltip with no goal value", () => {
    render(<PhasePerformanceChart phasePerformanceData={noGoalData} native={1} />)

    const phaseElement = screen.getByText("Planning").parentElement
    fireEvent.mouseEnter(phaseElement)
    expect(screen.getByText("Goal: -")).toBeInTheDocument()
  })
})
