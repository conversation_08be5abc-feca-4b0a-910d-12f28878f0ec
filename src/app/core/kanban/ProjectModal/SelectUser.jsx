import { FormControl, InputLabel, Select, MenuItem } from "@mui/material"
import { string, number, arrayOf, shape, func, object } from "prop-types"

const SelectUser = ({ label, value, setter, user, projectTeam }) => {
  return (
    <FormControl>
      <InputLabel>{label}</InputLabel>
      <Select
        data-testid="user-select"
        sx={{ width: 180 }}
        onChange={(e) => setter(e.target.value)}
        value={value}
        label={label}
        variant="standard"
      >
        {user && (
          <MenuItem value={""}>
            <em>None</em>
          </MenuItem>
        )}
        {projectTeam.map((userElement) => (
          <MenuItem key={userElement.userId.native} value={userElement.userId.native}>
            {userElement.fullname}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

export default SelectUser

SelectUser.propTypes = {
  label: string.isRequired,
  value: string.isRequired,
  setter: func.isRequired,
  user: string.isRequired,
  projectTeam: arrayOf(
    shape({
      fullname: string.isRequired,
      links: object,
      userId: shape({
        native: number.isRequired,
        partner: string
      }).isRequired,
      username: string.isRequired
    })
  )
}
