import { render, screen, act, fireEvent } from "@testing-library/react"
import ProjectActions from "./ProjectActions"

// Mock the entire firebase/functions module
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => ({}))
}))

// Mock the useProjectAttributes hook to return test data
const mockRefetch = jest.fn()
jest.mock("../../../../helpers/firebaseGetMethods", () => ({
  useProjectAttributes: jest.fn(() => ({
    data: {
      projectTeam: [
        {
          userId: { native: 1, partner: "partner1" },
          fullname: "<PERSON>",
          username: "johndo<PERSON>"
        }
      ],
      projectNoteList: [
        {
          author: "<PERSON>",
          body: "Test note",
          createdDate: "2024-01-01T12:00:00Z"
        }
      ]
    },
    isLoading: false,
    refetch: mockRefetch
  }))
}))

const mockProject = {
  links: {
    self: "url",
    projectType: "url",
    rootFolder: "url",
    client: "url",
    contacts: "url"
  },
  phaseName: "Planning",
  clientName: "Test Client",
  firstPrimaryUsername: "johndoe",
  firstPrimaryName: "John Doe",
  hashtags: ["test"],
  projectEmailAddress: "<EMAIL>",
  createdDate: "2024-01-01",
  projectId: { native: 123, partner: "partner123" },
  projectTypeId: { native: 456, partner: "partner456" },
  projectName: "Test Project",
  phaseId: { native: 789, partner: "partner789" },
  number: "TEST-001",
  projectUrl: "url",
  daysSpentInPhase: 5,
  formattedDate: "01/01/2024",
  index: 0
}

const defaultProps = {
  createNote: jest.fn(),
  project: mockProject
}

describe("ProjectActions", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders successfully with all components", () => {
    render(<ProjectActions {...defaultProps} />)
    expect(screen.getByText("Create project note...")).toBeInTheDocument()
  })

  it("handles successful note creation", async () => {
    const mockCreateNote = jest.fn().mockResolvedValue({
      data: {
        author: "John Doe",
        body: "New note",
        createdDate: "2024-01-01T12:00:00Z"
      }
    })

    render(<ProjectActions {...defaultProps} createNote={mockCreateNote} />)

    const textBox = screen.getByRole("textbox", { name: /create project note\.\.\./i })
    fireEvent.change(textBox, { target: { value: "New note" } })

    const submitButton = screen.getByRole("button", { name: /create note/i })
    await act(async () => {
      fireEvent.click(submitButton)
    })

    expect(mockCreateNote).toHaveBeenCalledWith({
      projectId: { native: 123 },
      body: "New note"
    })
    // Check if refetch was called instead of setNoteList
    expect(mockRefetch).toHaveBeenCalled()
  })

  it("handles note creation with user notification", async () => {
    const mockCreateNote = jest.fn().mockResolvedValue({
      data: {
        author: "John Doe",
        body: "+@johndoe New note",
        createdDate: "2024-01-01T12:00:00Z"
      }
    })

    render(<ProjectActions {...defaultProps} createNote={mockCreateNote} />)

    const textBox = screen.getByRole("textbox", { name: /create project note\.\.\./i })
    fireEvent.change(textBox, { target: { value: "New note" } })

    const userSelect = screen.getAllByRole("combobox")[0]
    fireEvent.mouseDown(userSelect)
    const option = screen.getByRole("option", { name: /John Doe/i })
    fireEvent.click(option)

    const submitButton = screen.getByRole("button", { name: /create note/i })
    await act(async () => {
      fireEvent.click(submitButton)
    })
    expect(mockCreateNote).toHaveBeenCalledWith({
      projectId: { native: 123 },
      body: "+@1 New note"
    })
    expect(mockRefetch).toHaveBeenCalled()
  })

  it("handles note creation with task assignment", async () => {
    const mockCreateNote = jest.fn().mockResolvedValue({
      data: {
        author: "John Doe",
        body: "New task",
        createdDate: "2024-01-01T12:00:00Z",
        assignee: "johndoe"
      }
    })

    render(<ProjectActions {...defaultProps} createNote={mockCreateNote} />)

    const textBox = screen.getByRole("textbox", { name: /create project note\.\.\./i })
    fireEvent.change(textBox, { target: { value: "New task" } })

    const userSelect = screen.getAllByRole("combobox")[1]
    fireEvent.mouseDown(userSelect)
    const option = screen.getByRole("option", { name: /John Doe/i })
    fireEvent.click(option)

    const submitButton = screen.getByRole("button", { name: /create task/i })
    await act(async () => {
      fireEvent.click(submitButton)
    })

    expect(mockCreateNote).toHaveBeenCalledWith({
      projectId: { native: 123 },
      body: "New task",
      assigneeId: { native: "1" }
    })
    expect(mockRefetch).toHaveBeenCalled()
  })

  it("handles note creation error", async () => {
    const mockCreateNote = jest.fn().mockRejectedValue("API Error")
    render(<ProjectActions {...defaultProps} createNote={mockCreateNote} />)

    await act(async () => {
      const textBox = screen.getByRole("textbox", { name: /create project note\.\.\./i })
      fireEvent.change(textBox, { target: { value: "New note" } })
      const button = screen.getByRole("button", { name: /create note/i })
      fireEvent.click(button)
    })

    expect(screen.getByText("error creating note")).toBeInTheDocument()
    expect(mockRefetch).not.toHaveBeenCalled()
  })

  it("shows loading state during note submission", async () => {
    const delay = 300
    const mockCreateNote = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, delay)))
    render(<ProjectActions {...defaultProps} createNote={mockCreateNote} />)

    await act(async () => {
      const button = screen.getByRole("button", {
        name: /create note/i
      })
      fireEvent.click(button)
    })

    expect(screen.getByText("Submitting Note...")).toBeInTheDocument()
  })
})
