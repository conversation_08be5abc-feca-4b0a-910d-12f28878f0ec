import { string, number, arrayOf, shape, bool, object, oneOfType } from "prop-types"
import { Stack, Typography } from "@mui/material"
import analyzePhases from "../helpers/analyzePhases"
import PhasePerformanceChart from "./PhasePerformanceChart"

const PhasePerformance = ({ project, data, phases }) => {
  const phasePerformanceData = analyzePhases(data, phases, project)

  return (
    <Stack width="100%" height="100%">
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ width: "100%" }}>
        <Typography variant="h4" style={{ marginBottom: "10px" }}>
          Phase Performance
        </Typography>
      </Stack>

      {data?.length > 0 && (
        <PhasePerformanceChart
          phasePerformanceData={phasePerformanceData}
          native={project?.phaseId?.native}
        />
      )}

      {(data?.length === 0 || phases?.length === 0) && <Typography>No changes were found.</Typography>}
    </Stack>
  )
}

export default PhasePerformance

PhasePerformance.propTypes = {
  project: shape({
    links: shape({
      self: string.isRequired,
      projectType: string.isRequired,
      rootFolder: string.isRequired,
      client: string.isRequired,
      contacts: string.isRequired
    }).isRequired,
    phaseName: string.isRequired,
    clientName: string.isRequired,
    firstPrimaryUsername: string.isRequired,
    firstPrimaryName: string.isRequired,
    hashtags: arrayOf(string).isRequired,
    projectEmailAddress: string.isRequired,
    createdDate: string.isRequired,
    projectId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectTypeId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    projectName: string.isRequired,
    phaseId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    number: string.isRequired,
    projectUrl: string.isRequired,
    daysSpentInPhase: number.isRequired,
    formattedDate: string.isRequired,
    index: number.isRequired
  }).isRequired,
  phases: arrayOf(
    shape({
      averageDays: number.isRequired,
      draggableId: string.isRequired,
      goal: number.isRequired,
      isPermanent: bool.isRequired,
      links: object.isRequired,
      name: string.isRequired,
      phaseDeadline: number.isRequired,
      phaseId: shape({
        native: number.isRequired,
        partner: oneOfType([string, number, object])
      }),
      usePhase: bool
    })
  ).isRequired,
  data: arrayOf(
    shape({
      dateChanged: string,
      fromPhaseId: number,
      fromPhaseName: string,
      fromTimestamp: number,
      toPhaseId: number,
      toPhaseName: string,
      toTimestamp: number,
      totalDaysSpent: number,
      userFullname: string,
      userId: number
    })
  ).isRequired
}
