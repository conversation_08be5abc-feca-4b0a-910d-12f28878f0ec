import { string, number, arrayOf, shape, func, object } from "prop-types"
import { Text<PERSON>ield, Stack, Typo<PERSON>, Button } from "@mui/material"
import SelectUser from "./SelectUser"

const ProjectActionsForm = ({
  taskUser,
  noteBody,
  setNoteBody,
  noteError,
  setNotifyUser,
  notifyUser,
  projectTeam,
  setTaskUser,
  postNote
}) => {
  return (
    <>
      <TextField
        id="outlined-multiline-static"
        label={!taskUser ? "Create project note..." : "Create project task..."}
        fullWidth
        multiline
        variant="standard"
        value={noteBody}
        onChange={(e) => setNoteBody(e.target.value)}
      />
      {noteError ? <Typography>error creating note</Typography> : <></>}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          marginTop: "20px"
        }}
      >
        <SelectUser
          label="Notify User"
          value={notifyUser}
          setter={setNotifyUser}
          user={notifyUser}
          projectTeam={projectTeam}
        />
        <SelectUser
          label="Task User"
          value={taskUser}
          setter={setTaskUser}
          user={taskUser}
          projectTeam={projectTeam}
        />

        <Button variant="contained" color="primary" onClick={() => postNote()}>
          Create {!taskUser ? "note" : "task"}
        </Button>
      </Stack>
    </>
  )
}

export default ProjectActionsForm

ProjectActionsForm.propTypes = {
  taskUser: string,
  noteBody: string,
  noteError: string,
  notifyUser: string,
  setNoteBody: func.isRequired,
  setNotifyUser: func.isRequired,
  setTaskUser: func.isRequired,
  postNote: func.isRequired,
  projectTeam: arrayOf(shape({
    fullname: string.isRequired,
    links: object,
    userId: shape({
      native: number.isRequired,
      partner: string
    }).isRequired,
    username: string.isRequired
  }))
}
