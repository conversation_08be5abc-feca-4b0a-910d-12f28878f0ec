import { shape, number, arrayOf, string } from "prop-types"
import SvgContainer from "@/charts/SvgContainer"
import React, { useState, useEffect, useRef } from "react"
import { scaleLinear } from "d3"
import { Stack } from "@mui/material"
import WrapText from "@/charts/WrapText"

const PhasePerformanceChart = ({ phasePerformanceData, native }) => {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [tooltip, setTooltip] = useState({ show: false, x: 0, y: 0, content: null })
  const [scrollOffset, setScrollOffset] = useState({ x: 0, y: 0 })
  const containerRef = useRef(null)
  const svgContainerRef = useRef(null)
  const phaseWidth = 120
  const BaseMargin = 10
  const phaseColumnNameHeight = 60
  const legendHeight = 90
  const barWidth = 30

  const yScale = scaleLinear(
    [0, phasePerformanceData.highestValue],
    [0, dimensions.height - (phaseColumnNameHeight + BaseMargin + legendHeight)]
  )

  const totalWidth = phasePerformanceData.data.length * phaseWidth

  const handleMouseEnter = (phase, event) => {
    const rect = containerRef.current.getBoundingClientRect()
    const svgRect = svgContainerRef.current.getBoundingClientRect()
    const x = event.clientX - svgRect.left + scrollOffset.x
    const y = event.clientY - rect.top
    setTooltip({
      x,
      y,
      show: true,
      content: {
        phaseName: phase.phaseName,
        goal: phase.goal
      }
    })
  }

  const handleMouseMove = (event) => {
    if (tooltip.show) {
      const rect = containerRef.current.getBoundingClientRect()
      const svgRect = svgContainerRef.current.getBoundingClientRect()
      const x = event.clientX - svgRect.left + scrollOffset.x
      const y = event.clientY - rect.top
      setTooltip((prev) => ({
        ...prev,
        x,
        y
      }))
    }
  }

  const handleScroll = (event) => {
    const container = event.target
    setScrollOffset({
      x: container.scrollLeft,
      y: container.scrollTop
    })
  }

  const handleMouseLeave = () => {
    setTooltip({ show: false, x: 0, y: 0, content: null })
  }

  useEffect(function captureDimensions() {
    if (containerRef.current) {
      setDimensions({
        width: containerRef.current.offsetWidth,
        height: containerRef.current.offsetHeight
      })
    }
  }, [])

  return (
    <Stack ref={containerRef} width="100%" height="100%">
      <SvgContainer
        dimensions={{ ...dimensions, totalWidth }}
        onScroll={handleScroll}
        svgRef={svgContainerRef}
      >
        {/* Legend */}
        <g transform={`translate(${scrollOffset.x}, 0)`}>
          <rect x={BaseMargin} y={10} width={40} height={30} fill="#B6D0F6" stroke="#80ACEF" />
          <rect x={BaseMargin} y={45} width={40} height={30} fill="#F6E8CB" stroke="#EDD097" />
          <rect x={BaseMargin + 350} y={23} width={40} height={3} fill="#CC2936" />
          <text x={65} y={30} style={{ fontWeight: "bold", fontSize: "14px" }}>
            Project: <tspan fontWeight="normal">Days in phase</tspan>
          </text>
          <text x={65 + 350} y={30} style={{ fontSize: "14px" }}>
            <tspan fontWeight="normal">Phase deadline</tspan>
          </text>
          <text x={65} y={65} style={{ fontWeight: "bold", fontSize: "14px" }}>
            Organization: <tspan fontWeight="normal">Average days in phase</tspan>
          </text>
        </g>

        {/* Scale */}
        <g>
          {Array.from({ length: phasePerformanceData.highestValue }, (_, i) => (
            <line
              key={`line-${i}`}
              x1={BaseMargin}
              y1={yScale(phasePerformanceData.highestValue) - yScale(i) + legendHeight}
              x2={totalWidth}
              y2={yScale(phasePerformanceData.highestValue) - yScale(i) + legendHeight}
              stroke="Gainsboro"
              strokeWidth={i % 7 === 0 ? "3" : "1"}
            />
          ))}
          <line
            x1={BaseMargin}
            y1={legendHeight}
            x2={dimensions.width}
            y2={legendHeight}
            stroke="Gainsboro"
          />
          {Array.from({ length: phasePerformanceData.highestValue }, (_, i) => (
            <text
              key={`line-${i}`}
              x={0}
              y={yScale(phasePerformanceData.highestValue) + legendHeight - yScale(i) + 3}
              stroke="DarkGray"
              fontSize="10px"
            >
              {i % 7 === 0 ? i : ""}
            </text>
          ))}
        </g>

        {/* Phases */}

        {phasePerformanceData.data.map((phase, index) => {
          return (
            <g
              key={phase.phaseId}
              transform={`translate(${index * phaseWidth + phaseWidth / 2}, 0)`}
              onMouseMove={handleMouseMove}
              onMouseLeave={handleMouseLeave}
              onMouseEnter={(e) => handleMouseEnter(phase, e)}
            >
              <rect
                x={-((phaseWidth - BaseMargin) / 2)}
                y={legendHeight}
                width={phaseWidth}
                height={dimensions.height - legendHeight > 0 ? dimensions.height - legendHeight : 0}
                fill={`${phase.phaseId === native ? "#e1b15319" : "#ffffff00"}`}
              />
              <WrapText
                text={phase.phaseName}
                width={60}
                x={5}
                y={dimensions.height - BaseMargin * 4}
                styles={{ fontWeight: "bold", fontSize: "14px" }}
                centered
              />
              <rect
                x={-barWidth}
                y={yScale(phasePerformanceData.highestValue) - yScale(phase.timeInPhase) + legendHeight}
                width={barWidth}
                height={yScale(phase.timeInPhase) > 0 ? yScale(phase.timeInPhase) : 0}
                fill="#B6D0F6"
                stroke="#80ACEF"
              />
              <rect
                x={BaseMargin}
                y={yScale(phasePerformanceData.highestValue) - yScale(phase.average) + legendHeight}
                width={barWidth}
                height={yScale(phase.average) > 0 ? yScale(phase.average) : 0}
                fill="#F6E8CB"
                stroke="#EDD097"
              />
              {phase.phaseDeadline && (
                <rect
                  x={-barWidth - 10}
                  y={yScale(phasePerformanceData.highestValue) - yScale(phase.phaseDeadline) + legendHeight}
                  width={barWidth + 21}
                  height={1.5}
                  fill="#CC2936"
                />
              )}
            </g>
          )
        })}

        {/* Tooltip */}
        {tooltip.show && (
          <g transform={`translate(${tooltip.x - scrollOffset.x + 10}, ${tooltip.y - 10})`}>
            <rect x={0} y={0} width={160} height={50} fill="white" stroke="#333" rx={4} />
            <text x={10} y={20} fill="#333" fontSize="14px" fontWeight="bold">
              {tooltip.content.phaseName}
            </text>
            <text x={10} y={40} fill="#333" fontSize="12px">
              Goal: {tooltip.content.goal ? `${tooltip.content.goal} days` : "-"}
            </text>
          </g>
        )}
      </SvgContainer>
    </Stack>
  )
}

export default PhasePerformanceChart

PhasePerformanceChart.propTypes = {
  phasePerformanceData: shape({
    highestValue: number.isRequired,
    data: arrayOf(
      shape({
        phaseName: string.isRequired,
        phaseId: number.isRequired,
        timeInPhase: number,
        average: number,
        goal: number
      })
    ).isRequired,
    maxTimeInPhase: number.isRequired,
    maxAverage: number.isRequired,
    maxGoal: number.isRequired
  }).isRequired,
  native: number.isRequired
}
