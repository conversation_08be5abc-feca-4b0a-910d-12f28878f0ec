import { render, screen } from "@testing-library/react"
import Details from "./Details"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

// Mock the firebaseGetMethods module
jest.mock("../../../../helpers/firebaseGetMethods", () => ({
  useProjectTypeList: jest.fn(() => ({
    data: [
      { projectTypeId: { native: 123 }, name: "Web Development" },
      { projectTypeId: { native: 456 }, name: "Mobile App" }
    ],
    isLoading: false
  }))
}))

describe("Details Component", () => {
  const mockProject = {
    clientName: "Test Client",
    phaseName: "Development",
    projectTypeId: { native: 123 },
    daysSpentInPhase: 5,
    formattedDate: "2024-01-01"
  }

  // Create a custom render function that provides the QueryClient
  const renderWithQueryClient = (ui) => {
    const testQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false
        }
      }
    })

    // Mock the project type list data in the query cache
    testQueryClient.setQueryData(
      ["projectTypeList"],
      [
        { projectTypeId: { native: 123 }, name: "Web Development" },
        { projectTypeId: { native: 456 }, name: "Mobile App" }
      ]
    )

    return render(<QueryClientProvider client={testQueryClient}>{ui}</QueryClientProvider>)
  }

  beforeEach(() => {
    // Clear mocks if needed
    jest.clearAllMocks()
  })

  test("renders all project details correctly", () => {
    renderWithQueryClient(<Details project={mockProject} />)

    expect(screen.getByText("Client Name:")).toBeInTheDocument()
    expect(screen.getByText("Test Client")).toBeInTheDocument()

    expect(screen.getByText("Phase:")).toBeInTheDocument()
    expect(screen.getByText("Development")).toBeInTheDocument()

    expect(screen.getByText("Project Type:")).toBeInTheDocument()
    expect(screen.getByText("Web Development")).toBeInTheDocument()

    expect(screen.getByText("Last activity:")).toBeInTheDocument()
    expect(screen.getByText(/days ago/)).toBeInTheDocument()

    expect(screen.getByText("Created Date:")).toBeInTheDocument()
    expect(screen.getByText("2024-01-01")).toBeInTheDocument()
  })

  test("handles project type not found", () => {
    const projectWithUnknownType = {
      ...mockProject,
      projectTypeId: { native: 999 }
    }

    const testQueryClient = new QueryClient()
    testQueryClient.setQueryData(
      ["projectTypeList"],
      [{ projectTypeId: { native: 456 }, name: "Mobile App" }]
    )

    render(
      <QueryClientProvider client={testQueryClient}>
        <Details project={projectWithUnknownType} />
      </QueryClientProvider>
    )

    expect(screen.getByText("Not found")).toBeInTheDocument()
  })

  test("handles different time periods for daysSpentInPhase", () => {
    const projectWithRecentActivity = {
      ...mockProject,
      // Less than an hour
      daysSpentInPhase: 0.02
    }

    renderWithQueryClient(<Details project={projectWithRecentActivity} />)
    expect(screen.getByText(/just now|minute/)).toBeInTheDocument()
  })

  test("handles empty project type list", () => {
    const testQueryClient = new QueryClient()
    testQueryClient.setQueryData(["projectTypeList"], [])

    render(
      <QueryClientProvider client={testQueryClient}>
        <Details project={mockProject} />
      </QueryClientProvider>
    )

    expect(screen.getByText("Not found")).toBeInTheDocument()
  })
})
