import { shape, string, arrayOf, number } from "prop-types"
import { Stack, Typography, Paper, List, Box } from "@mui/material"
import { MuiMarkdown } from "mui-markdown"

const NoteList = ({ noteList }) => {
  return (
    <List
      sx={{
        maxHeight: 500,
        overflow: "auto"
      }}
    >
      {noteList.length ? (
        noteList.map((note) => (
          <Box key={JSON.stringify(note)} sx={{ margin: "0 15px 15px 0" }}>
            <Paper
              elevation={3}
              sx={{
                padding: "10px",
                borderLeft: `3px solid ${!note.assignee ? "#e1b153" : "#0c2d5f"}`,
                backgroundColor: "white",
                overflowX: "auto",
                maxHeight: "400px",
                overflowY: "auto"
              }}
            >
              <Stack justifyContent="space-between" direction="row">
                <Typography variant="h6" sx={{ maxWidth: "50%" }}>
                  {note.author}:
                </Typography>
                {!note.assignee ? (
                  ""
                ) : (
                  <Typography variant="h6" sx={{ maxWidth: "50%" }}>
                    @{note.assignee}
                  </Typography>
                )}
              </Stack>
              <MuiMarkdown variant="h7" mode="markdown">
                {note.body}
              </MuiMarkdown>
              <Stack justifyContent="space-between">
                <Typography variant="subtitle2">{note.createdDate}</Typography>
                {note.completedOrTarget ? (
                  <Typography variant="subtitle2">{note.completedOrTarget}</Typography>
                ) : (
                  <></>
                )}
              </Stack>
            </Paper>
          </Box>
        ))
      ) : (
        <></>
      )}
    </List>
  )
}

NoteList.propTypes = {
  noteList: arrayOf(
    shape({
      author: string,
      body: string,
      createdDate: string,
      completedOrTarget: string,
      assignee: string,
      id: number
    })
  )
}

export default NoteList
