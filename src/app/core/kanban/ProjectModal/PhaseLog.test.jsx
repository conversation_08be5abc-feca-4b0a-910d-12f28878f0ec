import { render, screen } from "@testing-library/react"
import PhaseLog from "./PhaseLog"
import { getRelativeTime } from "../helpers/DateValidator"

jest.mock("../helpers/DateValidator", () => ({
  getRelativeTime: jest.fn()
}))

describe("PhaseLog", () => {
  const mockPhaseHistory = [
    {
      dateChanged: "2024-01-15",
      fromPhaseId: 1,
      fromPhaseName: "Planning",
      fromTimestamp: 1705305600000,
      toPhaseId: 2,
      toPhaseName: "Development",
      toTimestamp: 1705392000000,
      totalDaysSpent: 5,
      userFullname: "<PERSON>",
      userId: 1
    }
  ]

  beforeEach(() => {
    getRelativeTime.mockReturnValue("2 days ago")
  })

  test("renders empty state when no phase history", () => {
    render(<PhaseLog data={[]} />)
    expect(screen.getByText("No changes were found.")).toBeInTheDocument()
    expect(screen.getByText("Phase History")).toBeInTheDocument()
  })

  test("renders phase history table with data", () => {
    render(<PhaseLog data={mockPhaseHistory} />)

    expect(screen.getByRole("table")).toBeInTheDocument()
    expect(screen.getByText("Phase History")).toBeInTheDocument()
    expect(screen.getByText("John Doe")).toBeInTheDocument()
    expect(screen.getByText("Planning")).toBeInTheDocument()
    expect(screen.getByText("Development")).toBeInTheDocument()
    expect(screen.getByText("2 days ago")).toBeInTheDocument()
  })

  test("renders table headers correctly", () => {
    render(<PhaseLog data={mockPhaseHistory} />)

    const headers = ["Date", "Last modified", "Changed By", "From", "To"]
    headers.forEach((header) => {
      expect(screen.getByText(header)).toBeInTheDocument()
    })
  })

  test("displays phase history in reverse chronological order", () => {
    const multiplePhases = [
      {
        dateChanged: "2024-01-15",
        fromPhaseId: 1,
        fromPhaseName: "Planning",
        fromTimestamp: 1705305600000,
        toPhaseId: 2,
        toPhaseName: "Development",
        toTimestamp: 1705392000000,
        totalDaysSpent: 5,
        userFullname: "John Doe",
        userId: 1
      },
      {
        dateChanged: "2024-01-16",
        fromPhaseId: 2,
        fromPhaseName: "Development",
        fromTimestamp: 1705392000000,
        toPhaseId: 3,
        toPhaseName: "Testing",
        toTimestamp: 1705478400000,
        totalDaysSpent: 1,
        userFullname: "Jane Smith",
        userId: 2
      }
    ]

    render(<PhaseLog data={multiplePhases} />)

    const rows = screen.getAllByRole("row")
    expect(rows[1]).toHaveTextContent("Jane Smith")
    expect(rows[2]).toHaveTextContent("John Doe")
  })

  test("calls getRelativeTime with correct timestamp", () => {
    const logId = 1705392000000
    render(<PhaseLog data={mockPhaseHistory} />)
    expect(getRelativeTime).toHaveBeenCalledWith(logId)
  })
})
