import { render, screen } from "@testing-library/react"
import ProjectDetails from "./ProjectDetails"
import { LayoutContextProvider } from "../../LayoutContext"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { KanbanContextProvider } from "../KanbanContext"

jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => ({}))
}))

jest.mock("../../../../helpers/firebaseGetMethods", () => ({
  useProjectList: jest.fn(() => ({
    data: { projects: [], hashtagArray: [] },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),
  useProjectTypeList: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),
  useProjectPhaseList: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),

  useProjectAttributes: jest.fn(() => ({
    data: {
      vitals: [
        {
          fieldName: "test",
          fieldType: "string",
          friendlyName: "Test Field",
          links: {},
          position: 1,
          value: "test value"
        }
      ],
      phaseHistory: [
        {
          dateChanged: "2023-01-01",
          fromPhaseId: 1,
          fromPhaseName: "Planning",
          fromTimestamp: 1672531200000,
          toPhaseId: 2,
          toPhaseName: "Development",
          toTimestamp: 1672617600000,
          totalDaysSpent: 5,
          userFullname: "John Doe",
          userId: 123
        }
      ]
    },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  }))
}))

const mockProject = {
  links: {
    self: "http://test.com",
    projectType: "http://test.com/type",
    rootFolder: "http://test.com/folder",
    client: "http://test.com/client",
    contacts: "http://test.com/contacts"
  },
  phaseName: "Development",
  clientName: "Test Client",
  firstPrimaryUsername: "john.doe",
  firstPrimaryName: "John Doe",
  hashtags: ["test", "development"],
  projectEmailAddress: "<EMAIL>",
  createdDate: "2023-01-01",
  projectId: {
    native: 123,
    partner: "partner123"
  },
  projectTypeId: {
    native: 456,
    partner: "partner456"
  },
  projectName: "Test Project",
  phaseId: {
    native: 789,
    partner: "partner789"
  },
  number: "PRJ-001",
  projectUrl: "http://test.com/project",
  daysSpentInPhase: 10,
  formattedDate: "2023-01-01",
  index: 1
}

// Mock the child components to simplify tests
jest.mock("./Vitals", () => ({
  __esModule: true,
  default: () => <div data-testid="mock-vitals">Vitals Component</div>
}))

jest.mock("./PhaseLog", () => ({
  __esModule: true,
  default: () => <div data-testid="mock-phase-log">Phase History</div>
}))

jest.mock("./PhasePerformance", () => ({
  __esModule: true,
  default: () => <div data-testid="mock-phase-performance">Phase Performance</div>
}))

// Create a test QueryClient
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      }
    }
  })

const renderWithContext = (component) => {
  const queryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      <LayoutContextProvider>
        <KanbanContextProvider>{component}</KanbanContextProvider>
      </LayoutContextProvider>
    </QueryClientProvider>
  )
}

jest.mock("d3", () => ({
  scaleLinear: () => {
    const scale = (value) => value
    scale.domain = () => scale
    scale.range = () => scale
    return scale
  }
}))

describe("ProjectDetails", () => {
  beforeEach(() => {
    jest.spyOn(console, "log").mockImplementation(() => {})
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it("renders Vitals tab when selectedTab is 0", () => {
    renderWithContext(<ProjectDetails project={mockProject} selectedTab={0} />)
    expect(screen.getByTestId("mock-vitals")).toBeInTheDocument()
  })

  it("renders PhaseLog tab when selectedTab is 1", () => {
    renderWithContext(<ProjectDetails project={mockProject} selectedTab={1} />)
    expect(screen.getByTestId("mock-phase-log")).toBeInTheDocument()
  })

  it("renders PhasePerformance tab when selectedTab is 2", () => {
    renderWithContext(<ProjectDetails project={mockProject} selectedTab={2} />)
    expect(screen.getByTestId("mock-phase-performance")).toBeInTheDocument()
  })

  it("applies correct styling to container", () => {
    const { container } = renderWithContext(<ProjectDetails project={mockProject} selectedTab={0} />)
    const stack = container.firstChild
    expect(stack).toHaveStyle({ width: "60%" })
  })

  it("shows loading indicator when data is loading", () => {
    require("../../../../helpers/firebaseGetMethods").useProjectAttributes.mockReturnValueOnce({
      data: null,
      isLoading: true,
      isError: false,
      refetch: jest.fn()
    })

    renderWithContext(<ProjectDetails project={mockProject} selectedTab={0} />)
    expect(screen.getByRole("progressbar")).toBeInTheDocument()
  })
})
