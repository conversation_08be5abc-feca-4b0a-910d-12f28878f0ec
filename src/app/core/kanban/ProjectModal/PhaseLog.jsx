import {
  Stack,
  Typography,
  TableContainer,
  Table,
  TableRow,
  TableCell,
  TableHead,
  TableBody,
  Paper
} from "@mui/material"
import { number, arrayOf, shape, string } from "prop-types"
import { getRelativeTime } from "../helpers/DateValidator"

const PhaseLog = ({ data }) => {
  return (
    <Stack>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ width: "100%" }}>
        <Typography variant="h4" style={{ marginBottom: "10px" }}>
          Phase History
        </Typography>
      </Stack>

      {!data.length && <Typography>No changes were found.</Typography>}

      {data.length > 0 && (
        <TableContainer component={Paper} elevation={0} sx={{ maxHeight: 600 }}>
          <Table stickyHeader sx={{ minWidth: 650 }} size="small" aria-label="a dense table">
            <TableHeader />
            <TableBody>
              {/* slice() before reverse() to create a shallow copy first, since reverse() mutates the original array. */}
              {data
                .slice()
                .reverse()
                .map((row) => (
                  <TableRow key={row.toTimestamp} sx={{ "&:last-child td, &:last-child th": { border: 0 } }}>
                    <TableCell align="left">{row.dateChanged}</TableCell>
                    <TableCell align="right">{getRelativeTime(row.toTimestamp)}</TableCell>
                    <TableCell align="right">{row.userFullname}</TableCell>
                    <TableCell align="right">{row.fromPhaseName}</TableCell>
                    <TableCell align="right">{row.toPhaseName}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Stack>
  )
}

export default PhaseLog

PhaseLog.propTypes = {
  data: arrayOf(
    shape({
      dateChanged: string,
      fromPhaseId: number,
      fromPhaseName: string,
      fromTimestamp: number,
      toPhaseId: number,
      toPhaseName: string,
      toTimestamp: number,
      totalDaysSpent: number,
      userFullname: string,
      userId: number
    })
  ).isRequired
}

const TableHeader = () => {
  const HEADERS = ["Date", "Last modified", "Changed By", "From", "To"]
  return (
    <TableHead>
      <TableRow>
        {HEADERS.map((header) => (
          <TableCell key={header} sx={{ fontWeight: "bold" }} align={header === "Date" ? "left" : "right"}>
            {header}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}
