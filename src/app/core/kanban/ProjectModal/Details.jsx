import { string, number, shape, arrayOf, oneOfType } from "prop-types"
import { Typography, Stack, Box } from "@mui/material"
import { getRelativeTime } from "../helpers/DateValidator"
import { useQueryClient } from "@tanstack/react-query"

const VITALS = {
  clientName: "Client Name",
  phaseName: "Phase",
  projectTypeId: "Project Type",
  daysSpentInPhase: "Last activity",
  formattedDate: "Created Date"
}

const Details = ({ project }) => {
  const queryClient = useQueryClient()

  const projectTypeList = queryClient.getQueryData(["projectTypeList"])

  const formatValue = (key, value) => {
    if (key === "projectTypeId") {
      const projectType = projectTypeList.find((type) => type.projectTypeId.native === value.native)
      return projectType ? projectType.name : "Not found"
    }

    if (key === "daysSpentInPhase") {
      const msPerDay = 1000 * 60 * 60 * 24
      const timestamp = Date.now() - Math.round(value) * msPerDay
      return getRelativeTime(timestamp)
    }
    return value
  }

  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: "repeat(2, 1fr)"
      }}
    >
      {Object.keys(VITALS).map((key) => (
        <Stack key={key} direction="row">
          <Typography
            variant="h6"
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              marginRight: "10px"
            }}
          >
            {VITALS[key]}:
          </Typography>
          <Typography
            variant="h7"
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis"
            }}
          >
            {formatValue(key, project[key])}
          </Typography>
        </Stack>
      ))}
    </Box>
  )
}

Details.propTypes = {
  project: shape({
    links: shape({
      self: string,
      projectType: string,
      rootFolder: string,
      client: string,
      contacts: string
    }),
    phaseName: string,
    clientName: string,
    firstPrimaryUsername: string,
    firstPrimaryName: string,
    hashtags: arrayOf(string),
    projectEmailAddress: string,
    createdDate: string,
    projectId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectTypeId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectName: string,
    phaseId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    number: string,
    projectUrl: string,
    daysSpentInPhase: number,
    formattedDate: string,
    index: number
  }).isRequired
}

export default Details
