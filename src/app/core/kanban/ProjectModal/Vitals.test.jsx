import { render, screen, fireEvent } from "@testing-library/react"
import Vitals from "./Vitals"
import { dateValidator } from "../helpers/DateValidator"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

// Mock the firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => ({}))
}))

// Mock the Details component
jest.mock("./Details", () => ({
  __esModule: true,
  default: ({ project }) => <div data-testid="mock-details">Project Details: {project.projectName}</div>
}))

jest.mock("../helpers/DateValidator", () => ({
  dateValidator: jest.fn((value) => `Formatted: ${value}`)
}))

// Create a QueryClient for testing
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      }
    }
  })

const renderWithQueryClient = (component) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

describe("Vitals", () => {
  const mockProject = {
    projectUrl: "https://test.filevine.com",
    projectName: "Test Project",
    links: {
      self: "test",
      projectType: "test",
      rootFolder: "test",
      client: "test",
      contacts: "test"
    },
    phaseName: "Test Phase",
    clientName: "Test Client",
    firstPrimaryUsername: "test",
    firstPrimaryName: "test",
    hashtags: ["test"],
    projectEmailAddress: "<EMAIL>",
    createdDate: "2024-01-01",
    projectId: { native: 1, partner: "test" },
    projectTypeId: { native: 1, partner: "test" },
    phaseId: { native: 1, partner: "test" },
    number: "123",
    daysSpentInPhase: 5,
    formattedDate: "01/01/2024",
    index: 0
  }

  const mockVitals = [
    {
      friendlyName: "Created Date",
      value: "2024-01-15T12:00:00",
      fieldName: "createdDate",
      fieldType: "date"
    },
    {
      friendlyName: "Last Modified",
      value: "2024-01-16T15:30:00",
      fieldName: "lastModified",
      fieldType: "date"
    }
  ]

  const mockRefetch = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test("renders project details header and Filevine link", () => {
    renderWithQueryClient(
      <Vitals project={mockProject} vitals={mockVitals} loading={false} error={false} refetch={mockRefetch} />
    )

    expect(screen.getByText("Project Details")).toBeInTheDocument()
    const filevineLink = screen.getByRole("link")
    expect(filevineLink).toHaveAttribute("href", mockProject.projectUrl)
    expect(filevineLink).toHaveAttribute("target", "_blank")
    expect(filevineLink).toHaveAttribute("rel", "noreferrer")
  })

  test("renders vitals data correctly", () => {
    renderWithQueryClient(
      <Vitals project={mockProject} vitals={mockVitals} loading={false} error={false} refetch={mockRefetch} />
    )

    expect(screen.getByText("Created Date:")).toBeInTheDocument()
    expect(screen.getByText("Last Modified:")).toBeInTheDocument()
    expect(dateValidator).toHaveBeenCalledWith("2024-01-15T12:00:00")
    expect(dateValidator).toHaveBeenCalledWith("2024-01-16T15:30:00")
    expect(screen.getByText("Formatted: 2024-01-15T12:00:00")).toBeInTheDocument()
    expect(screen.getByText("Formatted: 2024-01-16T15:30:00")).toBeInTheDocument()
  })

  test("shows loading state", () => {
    renderWithQueryClient(
      <Vitals project={mockProject} vitals={[]} loading={true} error={false} refetch={mockRefetch} />
    )

    expect(screen.getByRole("progressbar")).toBeInTheDocument()
  })

  test("shows error state", () => {
    renderWithQueryClient(
      <Vitals project={mockProject} vitals={[]} loading={false} error={true} refetch={mockRefetch} />
    )

    expect(screen.getByText("Error loading project vitals")).toBeInTheDocument()
    expect(screen.getByRole("button")).toBeInTheDocument()
  })

  test("retry button handles click", () => {
    renderWithQueryClient(
      <Vitals project={mockProject} vitals={[]} loading={false} error={true} refetch={mockRefetch} />
    )

    const retryButton = screen.getByRole("button")
    expect(retryButton).toBeInTheDocument()
    fireEvent.click(retryButton)
    expect(mockRefetch).toHaveBeenCalledTimes(1)
  })
})
