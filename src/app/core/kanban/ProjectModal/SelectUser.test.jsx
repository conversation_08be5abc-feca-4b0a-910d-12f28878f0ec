import { render, screen, fireEvent, within } from "@testing-library/react"
import SelectUser from "./SelectUser"

describe("SelectUser", () => {
  const mockProps = {
    label: "Assign User",
    value: "1",
    setter: jest.fn(),
    user: "currentUser",
    projectTeam: [
      // Changed from 'users' to 'projectTeam'
      {
        userId: { native: 1, partner: "partner1" },
        fullname: "<PERSON>",
        username: "johndo<PERSON>"
      },
      {
        userId: { native: 2, partner: "partner2" },
        fullname: "<PERSON>",
        username: "jane<PERSON>"
      }
    ]
  }

  it("renders with all required props", () => {
    render(<SelectUser {...mockProps} />)
    expect(screen.getByTestId("user-select")).toBeInTheDocument()
    expect(screen.getByText("Assign User")).toBeInTheDocument()
  })

  it("displays all users in the dropdown", () => {
    render(<SelectUser {...mockProps} />)
    fireEvent.mouseDown(screen.getByRole("combobox"))

    const listbox = within(screen.getByRole("listbox"))
    const option1 = screen.getByRole("option", {
      name: /<PERSON>e/i
    })
    const option2 = screen.getByRole("option", {
      name: /Jane Smith/i
    })

    expect(listbox.getAllByRole("option")).toHaveLength(mockProps.projectTeam.length + 1)
    expect(option1).toBeInTheDocument()
    expect(option2).toBeInTheDocument()
  })

  it("shows 'None' option when user prop is provided", () => {
    render(<SelectUser {...mockProps} />)
    fireEvent.mouseDown(screen.getByRole("combobox"))

    const listbox = within(screen.getByRole("listbox"))
    const noneOption = screen.getByText(/none/i)

    expect(listbox.getAllByRole("option")).toHaveLength(mockProps.projectTeam.length + 1)
    expect(noneOption).toBeInTheDocument()
  })

  it("calls setter when a user is selected", () => {
    render(<SelectUser {...mockProps} />)
    fireEvent.mouseDown(screen.getByRole("combobox"))

    const listbox = within(screen.getByRole("listbox"))
    const option2 = screen.getByRole("option", {
      name: /Jane Smith/i
    })

    expect(listbox.getAllByRole("option")).toHaveLength(mockProps.projectTeam.length + 1)

    fireEvent.click(option2)

    expect(mockProps.setter).toHaveBeenCalledWith(2)
  })

  it("calls onChange when 'None' is selected", () => {
    render(<SelectUser {...mockProps} />)
    fireEvent.mouseDown(screen.getByRole("combobox"))

    const listbox = within(screen.getByRole("listbox"))
    const noneOption = screen.getByText(/none/i)

    expect(listbox.getAllByRole("option")).toHaveLength(mockProps.projectTeam.length + 1)

    fireEvent.click(noneOption)
    expect(mockProps.setter).toHaveBeenCalledWith("")
  })

  it("displays the correct selected value", () => {
    render(<SelectUser {...mockProps} />)
    const value = screen.getByText(/John Doe/i)
    expect(value).toBeInTheDocument()
  })
})
