import { render, screen } from "@testing-library/react"
import PhasePerformance from "./PhasePerformance"
import { useLayoutContext } from "../../LayoutContext"
import analyzePhases from "../helpers/analyzePhases"

jest.mock("../../LayoutContext")
jest.mock("../helpers/analyzePhases")
jest.mock("./PhasePerformanceChart", () => ({
  __esModule: true,
  default: ({ phasePerformanceData, native }) => (
    <div data-testid="mock-chart">
      Chart: {native} - {phasePerformanceData.data.length} phases
    </div>
  )
}))
jest.mock("d3", () => ({
  scaleLinear: () => {
    const scale = (value) => value
    scale.domain = () => scale
    scale.range = () => scale
    return scale
  }
}))

describe("PhasePerformance", () => {
  const mockProject = {
    phaseId: { native: 1 },
    links: {
      self: "test",
      projectType: "test",
      rootFolder: "test",
      client: "test",
      contacts: "test"
    },
    phaseName: "Test Phase",
    clientName: "Test Client",
    firstPrimaryUsername: "test",
    firstPrimaryName: "test",
    hashtags: ["test"],
    projectEmailAddress: "<EMAIL>",
    createdDate: "2024-01-01",
    projectId: { native: 1, partner: "test" },
    projectTypeId: { native: 1, partner: "test" },
    projectName: "Test Project",
    number: "123",
    projectUrl: "test.com",
    daysSpentInPhase: 5,
    formattedDate: "01/01/2024",
    index: 0
  }

  const mockPhaseList = [
    {
      averageDays: 4,
      draggableId: "990015863",
      goal: 10,
      isPermanent: false,
      links: {},
      name: "Phase 1",
      phaseDeadline: 20,
      phaseId: {
        native: 990015863,
        partner: null
      },
      usePhase: true
    }
  ]

  beforeEach(() => {
    useLayoutContext.mockReturnValue({
      projectPhaseList: mockPhaseList
    })
    analyzePhases.mockReturnValue({
      data: [{ phase: "Phase 1", timeInPhase: 5 }]
    })
  })

  test("renders loading state correctly", () => {
    render(<PhasePerformance data={[]} phases={mockPhaseList} project={mockProject} />)
    expect(screen.getByText("No changes were found.")).toBeInTheDocument()
  })

  test("renders empty state when no phase history", () => {
    render(<PhasePerformance data={[]} phases={mockPhaseList} project={mockProject} />)
    expect(screen.getByText("No changes were found.")).toBeInTheDocument()
  })

  test("renders phase performance chart when history exists", () => {
    const phaseHistory = [
      {
        dateChanged: "2024-01-01",
        fromPhaseId: 1,
        fromPhaseName: "Phase 1",
        fromTimestamp: 123456789,
        toPhaseId: 2,
        toPhaseName: "Phase 2",
        toTimestamp: 123456790,
        totalDaysSpent: 5,
        userFullname: "Test User",
        userId: 1
      }
    ]

    render(<PhasePerformance data={phaseHistory} phases={mockPhaseList} project={mockProject} />)

    expect(screen.getByText("Phase Performance")).toBeInTheDocument()
    expect(analyzePhases).toHaveBeenCalledWith(phaseHistory, mockPhaseList, mockProject)
    expect(screen.getByTestId("mock-chart")).toBeInTheDocument()
  })

  test("renders title correctly", () => {
    render(<PhasePerformance data={[]} phases={mockPhaseList} project={mockProject} />)
    expect(screen.getByText("Phase Performance")).toHaveStyle({ marginBottom: "10px" })
  })

  test("passes correct props to PhasePerformanceChart", () => {
    const phaseHistory = [
      {
        dateChanged: "2024-01-01",
        fromPhaseId: 1,
        fromPhaseName: "Initial Phase",
        fromTimestamp: 1672531200000,
        toPhaseId: 2,
        toPhaseName: "Next Phase",
        toTimestamp: 1672617600000,
        totalDaysSpent: 5,
        userFullname: "Test User",
        userId: 1
      }
    ]

    const mockAnalyzedData = {
      data: [{ phase: "Phase 1", timeInPhase: 5 }],
      highestValue: 5
    }
    analyzePhases.mockReturnValue(mockAnalyzedData)

    render(<PhasePerformance data={phaseHistory} phases={mockPhaseList} project={mockProject} />)

    const chart = screen.getByTestId("mock-chart")
    expect(chart).toHaveTextContent("Chart: 1 - 1")
  })
})
