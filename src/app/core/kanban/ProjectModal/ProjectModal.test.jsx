import { render, screen, fireEvent } from "@testing-library/react"
import ProjectModal from "./ProjectModal"
import { LayoutContextProvider } from "../../LayoutContext"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

// Mock the child components
jest.mock("./ProjectDetails", () => {
  return function MockProjectDetails() {
    return (
      <div>
        <h3>Project Details</h3>
        <div>Project Details</div>
        <div>Project Vitals</div>
      </div>
    )
  }
})

jest.mock("./ProjectActions", () => {
  return function MockProjectActions() {
    return (
      <div>
        <h3>Project Actions</h3>
        <div>Phase History</div>
        <div>Phase Performance</div>
      </div>
    )
  }
})

// Mock the firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => jest.fn()),
  getFunctions: jest.fn(() => ({}))
}))

// Mock the hooks from firebaseGetMethods.js
jest.mock("../../../../helpers/firebaseGetMethods", () => ({
  useProjectList: jest.fn(() => ({
    data: { projects: [], hashtagArray: [] },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),
  useProjectTypeList: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),
  useProjectPhaseList: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  })),
  useProjectAttributes: jest.fn(() => ({
    data: [
      {
        fieldName: "test",
        fieldType: "string",
        friendlyName: "Test Field",
        links: {},
        position: 1,
        value: "test value"
      }
    ],
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  }))
}))

jest.mock("d3", () => ({
  scaleLinear: () => {
    const scale = (value) => value
    scale.domain = () => scale
    scale.range = () => scale
    return scale
  }
}))

const mockProject = {
  links: {
    self: "http://test.com",
    projectType: "http://test.com/type",
    rootFolder: "http://test.com/folder",
    client: "http://test.com/client",
    contacts: "http://test.com/contacts"
  },
  phaseName: "Development",
  clientName: "Test Client",
  firstPrimaryUsername: "john.doe",
  firstPrimaryName: "John Doe",
  hashtags: ["test", "development"],
  projectEmailAddress: "<EMAIL>",
  createdDate: "2023-01-01",
  projectId: {
    native: 123,
    partner: "partner123"
  },
  projectTypeId: {
    native: 456,
    partner: "partner456"
  },
  projectName: "Test Project",
  phaseId: {
    native: 789,
    partner: "partner789"
  },
  number: "PRJ-001",
  projectUrl: "http://test.com/project",
  daysSpentInPhase: 10,
  formattedDate: "2023-01-01",
  index: 1
}

const mockPhaseHistory = [
  {
    dateChanged: "2023-01-01",
    fromPhaseId: 1,
    fromPhaseName: "Planning",
    fromTimestamp: 1672531200000,
    toPhaseId: 2,
    toPhaseName: "Development",
    toTimestamp: 1672617600000,
    totalDaysSpent: 5,
    userFullname: "John Doe",
    userId: 123
  }
]

const mockProjectTeam = [
  {
    links: {},
    userId: {
      native: 123,
      partner: "partner123"
    },
    fullname: "John Doe",
    username: "john.doe"
  }
]

const mockNoteList = [
  {
    author: "John Doe",
    body: "Test note",
    createdDate: "2023-01-01",
    assignee: "Jane Doe",
    completedOrTarget: "2023-01-02"
  }
]

const defaultProps = {
  openModal: true,
  handleClose: jest.fn(),
  project: mockProject,
  createNote: jest.fn()
}

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      }
    }
  })

const renderWithContext = (component) => {
  const queryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      <LayoutContextProvider>{component}</LayoutContextProvider>
    </QueryClientProvider>
  )
}

describe("ProjectModal", () => {
  it("renders modal with project name", () => {
    renderWithContext(<ProjectModal {...defaultProps} />)
    expect(screen.getByText("Test Project")).toBeInTheDocument()
  })

  it("handles tab navigation", () => {
    renderWithContext(<ProjectModal {...defaultProps} />)

    const projectDetailsTab = screen.getByRole("tab", {
      name: /project details/i
    })
    const phaseLogTab = screen.getByRole("tab", {
      name: /phase log/i
    })
    const phasePerformanceTab = screen.getByRole("tab", {
      name: /phase performance/i
    })

    fireEvent.click(phaseLogTab)
    expect(screen.getByText("Phase History")).toBeInTheDocument()

    fireEvent.click(phasePerformanceTab)
    expect(screen.getAllByText("Phase Performance")).toHaveLength(2)

    fireEvent.click(projectDetailsTab)
    expect(screen.getAllByText("Project Details")).toHaveLength(3)
    expect(screen.getByText("Project Vitals")).toBeInTheDocument()
  })

  it("closes modal when close button is clicked", () => {
    renderWithContext(<ProjectModal {...defaultProps} />)
    const closeButton = screen.getByTestId("CloseIcon").parentElement
    fireEvent.click(closeButton)
    expect(defaultProps.handleClose).toHaveBeenCalled()
  })

  it("renders modal with correct styling", () => {
    renderWithContext(<ProjectModal {...defaultProps} />)
    const modalContent = document.querySelector(".MuiModal-root")
    expect(modalContent).toBeInTheDocument()

    const modalStack = modalContent.querySelector(".MuiStack-root")
    expect(modalStack).toHaveStyle({
      position: "absolute",
      top: "50%",
      left: "50%"
    })
  })

  it("does not render when openModal is false", () => {
    renderWithContext(<ProjectModal {...defaultProps} openModal={false} />)
    expect(screen.queryByText("Test Project")).not.toBeInTheDocument()
  })
})
