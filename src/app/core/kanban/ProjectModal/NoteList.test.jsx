import { render, screen } from "@testing-library/react"
import NoteList from "./NoteList"

describe("NoteList", () => {
  const mockNoteList = [
    {
      author: "<PERSON>",
      body: "Test note content",
      createdDate: "2024-01-15",
      assignee: "jane",
      completedOrTarget: "2024-02-01"
    },
    {
      author: "<PERSON>",
      body: "Another test note",
      createdDate: "2024-01-16",
      assignee: "",
      completedOrTarget: ""
    }
  ]

  test("renders notes with all properties", () => {
    render(<NoteList noteList={mockNoteList} />)

    expect(screen.getByText("<PERSON>:")).toBeInTheDocument()
    expect(screen.getByText("@jane")).toBeInTheDocument()
    expect(screen.getByText("Test note content")).toBeInTheDocument()
    expect(screen.getByText("2024-01-15")).toBeInTheDocument()
    expect(screen.getByText("2024-02-01")).toBeInTheDocument()
  })

  test("renders notes without optional properties", () => {
    render(<NoteList noteList={mockNoteList} />)

    expect(screen.getByText("Jane Smith:")).toBeInTheDocument()
    expect(screen.getByText("Another test note")).toBeInTheDocument()
    expect(screen.getByText("2024-01-16")).toBeInTheDocument()
  })

  test("renders empty list when no notes provided", () => {
    render(<NoteList noteList={[]} />)
    const list = screen.getByRole("list")
    expect(list).toBeEmptyDOMElement()
  })

  test("renders markdown content correctly", () => {
    const noteWithMarkdown = [
      {
        author: "Mark Down",
        body: "**Bold text** and *italic*",
        createdDate: "2024-01-17",
        assignee: "mark",
        completedOrTarget: ""
      }
    ]

    render(<NoteList noteList={noteWithMarkdown} />)
    expect(screen.getByText("Bold text")).toBeInTheDocument()
  })

  test("applies correct styling based on assignee presence", () => {
    render(<NoteList noteList={mockNoteList} />)

    const notes = document.querySelectorAll("div.MuiPaper-root")
    expect(notes[0]).toHaveStyle({ borderLeft: "3px solid #0c2d5f" }) // with assignee
    expect(notes[1]).toHaveStyle({ borderLeft: "3px solid #e1b153" }) // without assignee
  })
})
