import { render, screen, fireEvent } from "@testing-library/react"
import ProjectActionsForm from "./ProjectActionsForm"

const mockProjectTeam = [
  {
    userId: { native: 1, partner: "partner1" },
    fullname: "<PERSON>",
    username: "joh<PERSON><PERSON>"
  },
  {
    userId: { native: 2, partner: "partner2" },
    fullname: "<PERSON>",
    username: "jane<PERSON>"
  }
]

const defaultProps = {
  taskUser: "",
  noteBody: "",
  setNoteBody: jest.fn(),
  noteError: "",
  setNotifyUser: jest.fn(),
  notifyUser: "",
  projectTeam: mockProjectTeam,
  setTaskUser: jest.fn(),
  postNote: jest.fn()
}

describe("ProjectActionsForm", () => {
  it("renders note creation form when no taskUser is set", () => {
    render(<ProjectActionsForm {...defaultProps} />)

    expect(screen.getByLabelText("Create project note...")).toBeInTheDocument()
    expect(screen.getByText("Create note")).toBeInTheDocument()
  })

  it("renders task creation form when taskUser is set", () => {
    render(<ProjectActionsForm {...defaultProps} taskUser="1" />)

    expect(screen.getByLabelText("Create project task...")).toBeInTheDocument()
    expect(screen.getByText("Create task")).toBeInTheDocument()
  })

  it("handles note body changes", () => {
    render(<ProjectActionsForm {...defaultProps} />)

    const input = screen.getByLabelText("Create project note...")
    fireEvent.change(input, { target: { value: "New note content" } })

    expect(defaultProps.setNoteBody).toHaveBeenCalledWith("New note content")
  })

  it("displays error message when noteError is present", () => {
    render(<ProjectActionsForm {...defaultProps} noteError="error creating note" />)

    expect(screen.getByText("error creating note")).toBeInTheDocument()
  })

  it("calls postNote when create button is clicked", () => {
    render(<ProjectActionsForm {...defaultProps} />)

    const createButton = screen.getByText("Create note")
    fireEvent.click(createButton)

    expect(defaultProps.postNote).toHaveBeenCalled()
  })

  it("renders with initial note body value", () => {
    render(<ProjectActionsForm {...defaultProps} noteBody="Initial note" />)

    expect(screen.getByLabelText("Create project note...")).toHaveValue("Initial note")
  })

  it("renders both SelectUser components with valid values", () => {
    render(<ProjectActionsForm {...defaultProps} notifyUser="1" taskUser="2" />)

    expect(screen.getByText("Notify User")).toBeInTheDocument()
    expect(screen.getByText("Task User")).toBeInTheDocument()
  })
})
