import { bool, func, shape, string, number, arrayOf, oneOfType } from "prop-types"
import React, { useState } from "react"
import { AppBar, Divider, IconButton, Typography, Modal, Stack, Tab, Tabs } from "@mui/material"
import CloseIcon from "@mui/icons-material/Close"
import ProjectDetails from "./ProjectDetails"
import ProjectActions from "./ProjectActions"

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "90%",
  minHeight: "500px",
  bgcolor: "background.paper",
  border: "3px solid #0c2d5f",
  outline: "none",
  borderRadius: "10px",
  boxShadow: 24,
  p: 4,
  padding: 0
}

function ProjectModal({ openModal, handleClose, project, createNote }) {
  const [selectedTab, setSelectedTab] = useState(0)

  const selectTab = (_e, newTab) => {
    setSelectedTab(newTab)
  }

  return (
    <Modal
      open={openModal}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Stack sx={{ ...style }}>
        <AppBar sx={{ position: "relative" }}>
          <Stack direction="row" justifyContent="space-between" sx={{ padding: "8px 0px 8px 24px" }}>
            <Tabs value={selectedTab} onChange={selectTab} textColor="secondary" indicatorColor="secondary">
              <Tab label="Project Details" sx={{ color: "white" }} />
              <Tab label="Phase Log" sx={{ color: "white" }} />
              <Tab label="Phase Performance" sx={{ color: "white" }} />
            </Tabs>
            <Stack direction="row" alignItems="center" gap="10px">
              <Typography variant="h4" marginRight="36px">
                {project.projectName}
              </Typography>
              <IconButton edge="start" color="inherit" onClick={handleClose}>
                <CloseIcon />
              </IconButton>
            </Stack>
          </Stack>
        </AppBar>
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{ padding: "25px", height: "100%", flex: 1, overflow: "auto", backgroundColor: "#f3f3f3" }}
        >
          <ProjectDetails project={project} selectedTab={selectedTab} />
          <Divider
            color="#e1b153"
            orientation="vertical"
            variant="middle"
            sx={{ margin: "0px 25px" }}
            flexItem
          />
          <ProjectActions createNote={createNote} project={project} />
        </Stack>
      </Stack>
    </Modal>
  )
}

export default ProjectModal

ProjectModal.propTypes = {
  openModal: bool.isRequired,
  handleClose: func.isRequired,
  project: shape({
    links: shape({
      self: string,
      projectType: string,
      rootFolder: string,
      client: string,
      contacts: string
    }),
    phaseName: string,
    clientName: string,
    firstPrimaryUsername: string,
    firstPrimaryName: string,
    hashtags: arrayOf(string),
    projectEmailAddress: string,
    createdDate: string,
    projectId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectTypeId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectName: string,
    phaseId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    number: string,
    projectUrl: string,
    daysSpentInPhase: number,
    formattedDate: string,
    index: number
  }).isRequired,
  createNote: func.isRequired
}
