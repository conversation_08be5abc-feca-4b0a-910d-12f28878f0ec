import { Draggable } from "@hello-pangea/dnd"
import { <PERSON>, CardContent, Stack, Typography, Chip } from "@mui/material"
import { arrayOf, shape, string, number, oneOfType, func } from "prop-types"
import { useState } from "react"
import handleUserColor from "./helpers/handleUserColor"
import { useQueryClient } from "@tanstack/react-query"
import { useKanbanContext } from "./KanbanContext"

import ProjectModal from "./ProjectModal/ProjectModal"
import getCurrentPhaseData from "./helpers/getCurrentPhaseData"
import { getBorderColor } from "./helpers/getBorderColor"

function ProjectCard({ project, position, createNote }) {
  const { projectTypeId } = useKanbanContext()
  const [openModal, setOpenModal] = useState(false)
  const queryClient = useQueryClient()

  const userColor = handleUserColor(project.firstPrimaryName)

  const phaseList = queryClient.getQueryData(["projectPhaseList", { projectTypeId: String(projectTypeId) }])

  const currentPhase = getCurrentPhaseData(project, phaseList)

  const handleOpen = () => {
    setOpenModal(true)
  }

  const handleClose = () => {
    setOpenModal(false)
  }

  return (
    <Draggable draggableId={String(project.projectId.native)} index={position}>
      {(provided, snapshot) => (
        <>
          <Card
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            ref={provided.innerRef}
            sx={{
              opacity: snapshot.isDragging ? 0.9 : 1,
              transform: snapshot.isDragging ? "rotate(-2deg)" : "",
              padding: "0 0 12px 0",
              borderLeft: `5px solid ${getBorderColor(project, currentPhase)}`,
              margin: "0 7px 10px 7px",
              boxShadow: "2px 4px 3px #aaa",
              "&:hover": {
                transform: "scale(1.05)"
              },
              backgroundColor: "white"
            }}
            elevation={snapshot.isDragging ? 3 : 1}
            onClick={handleOpen}
          >
            <CardContent>
              <Typography variant="h6" component="div">
                {project.projectName}
              </Typography>
              <Typography variant="body2">{project.clientName}</Typography>
              <Typography sx={{ fontSize: 12 }} color="text.secondary">
                {project.formattedDate}
              </Typography>
            </CardContent>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="body2" sx={{ marginLeft: "16px" }}>
                {project.daysSpentInPhase ? `${project.daysSpentInPhase} Days` : <></>}
              </Typography>
              <Chip
                label={userColor.name}
                color="primary"
                size="small"
                sx={{
                  marginRight: "10px",
                  fontSize: "10px",
                  fontWeight: "bold",
                  backgroundColor: userColor.color,
                  color: "black"
                }}
              />
            </Stack>
          </Card>
          <ProjectModal
            openModal={openModal}
            handleClose={handleClose}
            project={project}
            createNote={createNote}
          />
        </>
      )}
    </Draggable>
  )
}

export default ProjectCard

ProjectCard.propTypes = {
  project: shape({
    links: shape({
      self: string,
      projectType: string,
      rootFolder: string,
      client: string,
      contacts: string
    }),
    phaseName: string,
    clientName: string,
    firstPrimaryUsername: string,
    firstPrimaryName: string,
    hashtags: arrayOf(string),
    projectEmailAddress: string,
    createdDate: string,
    projectId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectTypeId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectName: string,
    phaseId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    number: string,
    projectUrl: string,
    daysSpentInPhase: number,
    formattedDate: string,
    index: number
  }).isRequired,
  position: number.isRequired,
  createNote: func.isRequired,
  onHeightChange: func.isRequired
}
