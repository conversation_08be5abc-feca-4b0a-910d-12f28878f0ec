import { NativeSelect, Box, Typography, Stack } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import { string, arrayOf, func, number } from "prop-types"
import ProjectsLegend from "./ProjectsLegend"

function ProjectFilters({
  currentProjectPrimary,
  setCurrentProjectPrimary,
  setCurrentHashtag,
  currentHashtag,
  projectPrimaryArray,
  projectHashtagArray,
  projectTypeId,
  setProjectTypeId
}) {
  const queryClient = useQueryClient()

  const projectTypeList = queryClient.getQueryData(["projectTypeList"])

  const selectProjectType = (e) => {
    setCurrentProjectPrimary("All Primaries")
    setCurrentHashtag("")
    setProjectTypeId(e.target.value)
  }

  const selectPrimary = (e) => {
    setCurrentProjectPrimary(e.target.value)
  }

  const selectHashtag = (e) => {
    setCurrentHashtag(e.target.value)
  }

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        width: "calc(100vw - 90px)",
        padding: "30px"
      }}
    >
      <Stack direction="row">
        <Box sx={{ width: 250 }}>
          <Typography variant="h4">Project Type</Typography>
          <NativeSelect sx={{ width: 200 }} value={projectTypeId} onChange={selectProjectType}>
            {projectTypeList.map((type) => (
              <option key={type.projectTypeId.native} value={type.projectTypeId.native}>
                {type.name}
              </option>
            ))}
          </NativeSelect>
        </Box>
        <Box sx={{ width: 250 }}>
          <Typography variant="h4">Project Primary</Typography>
          <NativeSelect value={currentProjectPrimary} onChange={selectPrimary} sx={{ width: 200 }}>
            {projectPrimaryArray.map((username) => (
              <option key={username} value={username}>
                {username}
              </option>
            ))}
          </NativeSelect>
        </Box>
        <Box sx={{ width: 250 }}>
          <Typography variant="h4">Hashtag</Typography>
          <NativeSelect value={currentHashtag} onChange={(e) => selectHashtag(e)} sx={{ width: 200 }}>
            {projectHashtagArray.map((tag) => (
              <option key={tag} value={tag}>
                {tag}
              </option>
            ))}
          </NativeSelect>
        </Box>
      </Stack>
      <ProjectsLegend />
    </Stack>
  )
}

export default ProjectFilters

ProjectFilters.propTypes = {
  currentProjectPrimary: string.isRequired,
  setCurrentProjectPrimary: func.isRequired,
  currentHashtag: string.isRequired,
  setCurrentHashtag: func.isRequired,
  projectPrimaryArray: arrayOf(string).isRequired,
  projectHashtagArray: arrayOf(string).isRequired,
  projectTypeId: number.isRequired,
  setProjectTypeId: func.isRequired
}
