import processName from "./processName"
import { earthyPastelColors } from "@/helpers/earthColors"

const handleUserColor = (name) => {
  const rawStoredUserColor = localStorage.getItem("userColors") ?? "[]"
  const storedUserColors = JSON.parse(rawStoredUserColor)
  const processedName = processName(name)
  const existingUser = storedUserColors.find((user) => user.name === processedName)

  if (existingUser) {
    return existingUser
  } else {
    const randomIndex = Math.floor(Math.random() * earthyPastelColors.length)
    const randomColor = earthyPastelColors[randomIndex]

    const newUserColor = {
      name: processedName,
      color: randomColor
    }
    storedUserColors.push(newUserColor)
    localStorage.setItem("userColors", JSON.stringify(storedUserColors))

    return newUserColor
  }
}

export default handleUserColor
