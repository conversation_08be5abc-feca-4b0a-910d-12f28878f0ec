import getPhaseInfo from "./getPhaseInfo"

describe("getPhaseInfo", () => {
  test("returns project count when usePhase is true", () => {
    const phase = {
      name: "Development",
      usePhase: true
    }
    const projects = {
      projectArray: [{ id: 1 }, { id: 2 }, { id: 3 }]
    }

    expect(getPhaseInfo(phase, projects)).toBe("(3)")
  })

  test("returns project count when usePhase property is not present", () => {
    const phase = {
      name: "Development"
      // usePhase property not present
    }
    const projects = {
      projectArray: [{ id: 1 }, { id: 2 }]
    }

    expect(getPhaseInfo(phase, projects)).toBe("(2)")
  })

  test("returns empty string when usePhase is false and isPermanent is true", () => {
    const phase = {
      name: "Archived",
      usePhase: false,
      isPermanent: true
    }
    const projects = {
      projectArray: [{ id: 1 }, { id: 2 }]
    }

    expect(getPhaseInfo(phase, projects)).toBe("")
  })

  test('returns "(hidden)" when usePhase is false and isPermanent is false', () => {
    const phase = {
      name: "Development",
      usePhase: false,
      isPermanent: false
    }
    const projects = {
      projectArray: [{ id: 1 }]
    }

    expect(getPhaseInfo(phase, projects)).toBe("(hidden)")
  })

  test("handles null projects gracefully", () => {
    const phase = {
      name: "Development",
      usePhase: true
    }

    // When projects is null/undefined, the optional chaining operator handles it
    expect(getPhaseInfo(phase, null)).toBe("(undefined)")
  })

  test("handles undefined projects gracefully", () => {
    const phase = {
      name: "Development",
      usePhase: true
    }

    expect(getPhaseInfo(phase, undefined)).toBe("(undefined)")
  })

  // We don't test with {} as projects since it would cause an error
  // The function only handles null/undefined with optional chaining
  // but doesn't check if projectArray exists
})
