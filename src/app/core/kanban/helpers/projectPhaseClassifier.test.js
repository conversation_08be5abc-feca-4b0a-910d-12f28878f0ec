import { projectPhaseClassifier, getPrimaryList, getHashtagList } from "./projectPhaseClassifier"

describe("projectPhaseClassifier", () => {
  // Mock data for tests
  const mockPhaseList = [
    { phaseId: { native: 1 }, name: "Planning", usePhase: true },
    { phaseId: { native: 2 }, name: "Development", usePhase: true },
    { phaseId: { native: 3 }, name: "Testing", usePhase: false },
    { phaseId: { native: 4 }, name: "Deployment" }
  ]

  const mockProjectList = [
    {
      phaseId: { native: 1 },
      firstPrimaryName: "<PERSON>",
      name: "Project A",
      hashtags: ["#frontend", "#urgent"]
    },
    {
      phaseId: { native: 2 },
      firstPrimaryName: "<PERSON>",
      name: "Project B",
      hashtags: ["#backend"]
    },
    {
      phaseId: { native: 1 },
      firstPrimaryName: "<PERSON>",
      name: "Project C",
      hashtags: ["#frontend", "#design"]
    },
    {
      phaseId: { native: 3 },
      firstPrimaryName: "<PERSON>",
      name: "Project D",
      hashtags: ["#qa", "#urgent"]
    },
    {
      phaseId: { native: 4 },
      firstPrimaryName: "<PERSON>e",
      name: "Project E",
      hashtags: ["#deployment"]
    },
    {
      phaseId: { native: 5 },
      firstPrimaryName: "Bob Brown",
      name: "Project F",
      hashtags: ["#planning"]
    },
    {
      phaseId: { native: 1 },
      firstPrimaryName: null,
      name: "Project G",
      hashtags: []
    }
  ]

  test("returns the correct structure with phases and primary list", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries")

    expect(result).toHaveProperty("phase")
    expect(result).toHaveProperty("primaryList")
    expect(result).toHaveProperty("hashtagList")
    expect(result.primaryList).toContain("All Primaries")
    expect(result.primaryList).toContain("John Doe")
    expect(result.primaryList).toContain("Jane Smith")
    expect(result.primaryList).toContain("Bob Brown")
    expect(result.hashtagList).toContain("All Hashtags")
  })

  test("assigns sequential indices to projects regardless of phase", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries")

    // Collect all projects from all phases
    const allProjects = [
      ...result.phase[1].projectArray,
      ...result.phase[2].projectArray,
      ...result.phase[4].projectArray
    ]

    expect(allProjects.length).toBe(5)

    // Sort by index to check sequentiality
    const sortedIndices = [...allProjects.map((p) => p.index)].sort((a, b) => a - b)
    expect(sortedIndices).toEqual([0, 1, 2, 3, 4])

    // Verify each project has a unique index
    const uniqueIndices = new Set(allProjects.map((p) => p.index))
    expect(uniqueIndices.size).toBe(allProjects.length)
  })

  test("filters projects by primary when a primary filter is set", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "John Doe")

    expect(result.phase[1].projectArray.length).toBe(1)
    expect(result.phase[1].projectArray[0].name).toBe("Project A")

    expect(result.phase[2].projectArray.length).toBe(1)
    expect(result.phase[2].projectArray[0].name).toBe("Project B")

    expect(result.phase[4].projectArray.length).toBe(1)
    expect(result.phase[4].projectArray[0].name).toBe("Project E")
  })

  test("filters projects by hashtag when a hashtag filter is set", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries", "#frontend")

    expect(result.phase[1].projectArray.length).toBe(2)
    const projectNames = result.phase[1].projectArray.map((p) => p.name)
    expect(projectNames).toContain("Project A")
    expect(projectNames).toContain("Project C")

    // No projects in phase 2 or 4 have #frontend
    expect(result.phase[2].projectArray.length).toBe(0)
    expect(result.phase[4].projectArray.length).toBe(0)
  })

  test("filters projects by both primary and hashtag when both filters are set", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "John Doe", "#urgent")

    // Only Project A from John Doe has #urgent
    expect(result.phase[1].projectArray.length).toBe(1)
    expect(result.phase[1].projectArray[0].name).toBe("Project A")

    // No other projects from John Doe have #urgent
    expect(result.phase[2].projectArray.length).toBe(0)
    expect(result.phase[4].projectArray.length).toBe(0)
  })

  test("includes all projects when 'All Primaries' filter is set", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries")

    expect(result.phase[1].projectArray.length).toBe(3)
    expect(result.phase[2].projectArray.length).toBe(1)
    expect(result.phase[3].projectArray.length).toBe(0)
    expect(result.phase[4].projectArray.length).toBe(1)
  })

  test("includes all hashtags when 'All Hashtags' filter is set", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries", "All Hashtags")

    expect(result.phase[1].projectArray.length).toBe(3)
    expect(result.phase[2].projectArray.length).toBe(1)
    expect(result.phase[4].projectArray.length).toBe(1)
  })

  test("handles null filters by using defaults", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, null, null)

    // Should use the first project's primary as default (John Doe)
    expect(result.phase[1].projectArray.length).toBe(1)
    expect(result.phase[1].projectArray[0].name).toBe("Project A")
  })

  test("handles undefined hashtag filter", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "John Doe", undefined)

    // Should include all of John Doe's projects
    expect(result.phase[1].projectArray.length).toBe(1)
    expect(result.phase[2].projectArray.length).toBe(1)
    expect(result.phase[4].projectArray.length).toBe(1)
  })

  test("respects usePhase flag", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries")

    // Phase 3 has usePhase: false, so no projects should be in it
    expect(result.phase[3].projectArray.length).toBe(0)
  })

  test("sets default usePhase to true when not specified", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "All Primaries")

    // Phase 4 doesn't have usePhase property, should default to true
    expect(result.phase[4].usePhase).toBe(true)
    expect(result.phase[4].projectArray.length).toBe(1)
  })

  test("ignores projects with phases not in the phase list", () => {
    const result = projectPhaseClassifier(mockProjectList, mockPhaseList, "Bob Brown")

    // Project F has phaseId 5 which is not in the phase list
    expect(Object.keys(result.phase)).not.toContain("5")

    // No projects should be classified for Bob Brown
    Object.values(result.phase).forEach((phase) => {
      expect(phase.projectArray.every((p) => p.firstPrimaryName !== "Bob Brown")).toBe(true)
    })
  })

  test("handles empty project list", () => {
    const result = projectPhaseClassifier([], mockPhaseList, "All Primaries")

    expect(result.primaryList).toEqual(["All Primaries"])
    expect(result.hashtagList).toEqual(["All Hashtags"])
    expect(Object.values(result.phase).every((p) => p.projectArray.length === 0)).toBe(true)
  })

  test("handles empty phase list", () => {
    const result = projectPhaseClassifier(mockProjectList, [], "All Primaries")

    expect(result.phase).toEqual({})
    expect(result.primaryList).toContain("All Primaries")
    expect(result.primaryList).toContain("John Doe")
  })

  test("handles project with missing hashtags property", () => {
    const projectListWithMissingHashtags = [
      ...mockProjectList,
      {
        phaseId: { native: 1 },
        firstPrimaryName: "Test User",
        name: "Project Without Hashtags"
        // No hashtags property
      }
    ]

    // Should not throw an error
    const result = projectPhaseClassifier(projectListWithMissingHashtags, mockPhaseList, "All Primaries")

    // Should include the project without hashtags
    const phase1Projects = result.phase[1].projectArray.map((p) => p.name)
    expect(phase1Projects).toContain("Project Without Hashtags")
  })
})

describe("getPrimaryList", () => {
  test("extracts unique primary names from project list", () => {
    const projectList = [
      { firstPrimaryName: "John Doe" },
      { firstPrimaryName: "Jane Smith" },
      { firstPrimaryName: "John Doe" }, // Duplicate
      { firstPrimaryName: "Bob Brown" }
    ]

    const result = getPrimaryList(projectList)

    expect(result).toEqual(["Bob Brown", "Jane Smith", "John Doe"])
  })

  test("filters out null or undefined primary names", () => {
    const projectList = [
      { firstPrimaryName: "John Doe" },
      { firstPrimaryName: null },
      { firstPrimaryName: null },
      { firstPrimaryName: "Jane Smith" }
    ]

    const result = getPrimaryList(projectList)

    expect(result).toEqual(["Jane Smith", "John Doe"])
  })

  test("returns empty array for empty project list", () => {
    const result = getPrimaryList([])

    expect(result).toEqual([])
  })

  test("sorts primary names alphabetically", () => {
    const projectList = [
      { firstPrimaryName: "Zach" },
      { firstPrimaryName: "Bob" },
      { firstPrimaryName: "Alice" }
    ]

    const result = getPrimaryList(projectList)

    expect(result).toEqual(["Alice", "Bob", "Zach"])
  })
})

describe("getHashtagList", () => {
  test("extracts unique hashtags from all projects", () => {
    const projectList = [
      { hashtags: ["#frontend", "#urgent"] },
      { hashtags: ["#backend"] },
      // Duplicate #frontend
      { hashtags: ["#frontend", "#design"] }
    ]

    const result = getHashtagList(projectList)

    expect(result).toEqual(["#backend", "#design", "#frontend", "#urgent"])
  })

  test("returns empty array for empty project list", () => {
    const result = getHashtagList([])

    expect(result).toEqual([])
  })

  test("handles projects with empty hashtag arrays", () => {
    const projectList = [{ hashtags: ["#frontend"] }, { hashtags: [] }, { hashtags: ["#backend"] }]

    const result = getHashtagList(projectList)

    expect(result).toEqual(["#backend", "#frontend"])
  })

  test("handles projects with missing hashtags property", () => {
    const projectList = [
      { hashtags: ["#frontend"] },
      // Missing hashtags property
      {},
      { hashtags: ["#backend"] }
    ]

    const result = getHashtagList(projectList)

    expect(result).toEqual(["#backend", "#frontend"])
  })

  test("sorts hashtags alphabetically", () => {
    const projectList = [{ hashtags: ["#zebra"] }, { hashtags: ["#apple"] }, { hashtags: ["#banana"] }]

    const result = getHashtagList(projectList)

    expect(result).toEqual(["#apple", "#banana", "#zebra"])
  })
})
