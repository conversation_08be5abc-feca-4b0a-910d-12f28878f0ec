export const updateLocalPhaseStatus = (sourceProject, source, destination, sortedProjectList) => {
  const sourceColumn = sortedProjectList[source.status]
  const destinationColumn = sortedProjectList[destination.status]

  sourceColumn.projectArray.splice(source.index, 1)

  const modifiedProject = {
    ...sourceProject,
    // Only reset daysSpentInPhase when moving to a different phase
    daysSpentInPhase: source.status !== destination.status ? 0.1 : sourceProject.daysSpentInPhase
  }

  destinationColumn.projectArray.splice(
    destination.index ?? destinationColumn.projectArray.length + 1,
    0,
    modifiedProject
  )

  return {
    ...sortedProjectList,
    [source.status]: sourceColumn,
    [destination.status]: destinationColumn
  }
}

export default updateLocalPhaseStatus
