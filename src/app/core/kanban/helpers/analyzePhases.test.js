import analyzePhases from "./analyzePhases"

describe("analyzePhases", () => {
  const mockPhaseList = [
    {
      name: "Planning",
      phaseId: { native: 1 },
      averageDays: 10,
      goal: 15
    },
    {
      name: "Development",
      phaseId: { native: 2 },
      averageDays: 20,
      goal: 25
    },
    {
      name: "Testing",
      phaseId: { native: 3 }
    }
  ]

  it("calculates phase durations correctly", () => {
    const phaseHistory = [
      { fromPhaseId: 1, totalDaysSpent: 5 },
      { fromPhaseId: 1, totalDaysSpent: 7 },
      { fromPhaseId: 2, totalDaysSpent: 15 }
    ]

    const result = analyzePhases(phaseHistory, mockPhaseList)

    expect(result.data).toHaveLength(3)
    expect(result.data[0].timeInPhase).toBe(12)
    expect(result.data[1].timeInPhase).toBe(15)
    expect(result.data[2].timeInPhase).toBe(0)
  })

  it("includes current project phase data", () => {
    const phaseHistory = [{ fromPhaseId: 1, totalDaysSpent: 5 }]
    const project = {
      phaseId: { native: 2 },
      daysSpentInPhase: 10
    }

    const result = analyzePhases(phaseHistory, mockPhaseList, project)

    expect(result.data[0].timeInPhase).toBe(5)
    expect(result.data[1].timeInPhase).toBe(10)
  })

  it("combines historical and current phase data", () => {
    const phaseHistory = [{ fromPhaseId: 2, totalDaysSpent: 8 }]
    const project = {
      phaseId: { native: 2 },
      daysSpentInPhase: 7
    }

    const result = analyzePhases(phaseHistory, mockPhaseList, project)

    expect(result.data[1].timeInPhase).toBe(15)
  })

  it("handles empty phase history with current project", () => {
    const project = {
      phaseId: { native: 1 },
      daysSpentInPhase: 3
    }

    const result = analyzePhases([], mockPhaseList, project)

    expect(result.data[0].timeInPhase).toBe(3)
    expect(result.data[1].timeInPhase).toBe(0)
    expect(result.data[2].timeInPhase).toBe(0)
  })

  it("handles missing project daysSpentInPhase", () => {
    const project = {
      phaseId: { native: 1 }
    }

    const result = analyzePhases([], mockPhaseList, project)

    expect(result.data[0].timeInPhase).toBe(0)
  })

  it("handles empty phase history", () => {
    const result = analyzePhases([], mockPhaseList)

    expect(result.data).toHaveLength(3)
    expect(result.data[0].timeInPhase).toBe(0)
    expect(result.data[1].timeInPhase).toBe(0)
    expect(result.data[2].timeInPhase).toBe(0)
  })

  it("calculates highest value correctly", () => {
    const phaseHistory = [
      { fromPhaseId: 1, totalDaysSpent: 30 },
      { fromPhaseId: 2, totalDaysSpent: 15 }
    ]

    const result = analyzePhases(phaseHistory, mockPhaseList)

    expect(result.highestValue).toBe(30)
    expect(result.maxTimeInPhase).toBe(30)
    expect(result.maxAverage).toBe(20)
    expect(result.maxGoal).toBe(25)
  })

  it("handles missing average and goal values", () => {
    const phaseHistory = [{ fromPhaseId: 3, totalDaysSpent: 10 }]

    const result = analyzePhases(phaseHistory, mockPhaseList)

    expect(result.data[2].average).toBe(0)
    expect(result.data[2].goal).toBe(0)
  })
})
