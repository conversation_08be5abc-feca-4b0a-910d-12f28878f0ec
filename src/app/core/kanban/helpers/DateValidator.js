export const dateValidator = (string) => {
  if (!string) {
    return undefined
  }

  const check = string.split("T")
  if (check.length === 2 && check[0].length === 10) {
    const dateObj = new Date(string)
    let day = dateObj.getDate()
    day = day < 10 ? "0" + day : day
    let month = dateObj.getMonth() + 1
    month = month < 10 ? "0" + month : month
    const year = dateObj.getFullYear()

    return `${month}/${day}/${year}`
  } else {
    return string
  }
}
export const getRelativeTime = (timestamp) => {
  const now = new Date()
  const daysPerWeek = 7
  const daysPerMonth = 30.44
  const daysPerYear = 365.25
  const diffInMilliseconds = now - timestamp
  const diffInSeconds = Math.floor(diffInMilliseconds / 1000)
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  const diffInHours = Math.floor(diffInMinutes / 60)
  const diffInDays = Math.floor(diffInHours / 24)
  const diffInWeeks = Math.floor(diffInDays / daysPerWeek)
  const diffInMonths = Math.floor(diffInDays / daysPerMonth)
  const diffInYears = Math.floor(diffInDays / daysPerYear)

  const timeUnits = [
    { value: diffInYears, unit: "year" },
    { value: diffInMonths, unit: "month" },
    { value: diffInWeeks, unit: "week" },
    { value: diffInDays, unit: "day" },
    { value: diffInHours, unit: "hour" },
    { value: diffInMinutes, unit: "minute" }
  ]

  for (const { value, unit } of timeUnits) {
    if (value > 0) {
      return `${value} ${value === 1 ? unit : unit + "s"} ago`
    }
  }

  return "just now"
}
