import { dateValidator, getRelativeTime } from "./DateValidator"

describe("dateValidator", () => {
  test("formats ISO date string correctly", () => {
    const input = "2024-01-15T12:00:00"
    expect(dateValidator(input)).toBe("01/15/2024")
  })

  test("handles single digit month and day", () => {
    const input = "2024-02-05T12:00:00"
    expect(dateValidator(input)).toBe("02/05/2024")
  })

  test("returns original string if not ISO format", () => {
    const input = "2024-01-15"
    expect(dateValidator(input)).toBe("2024-01-15")
  })

  test("handles undefined input", () => {
    expect(dateValidator(undefined)).toBeUndefined()
  })
})

describe("getRelativeTime", () => {
  beforeEach(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date("2024-01-15T12:00:00"))
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  test("returns years ago", () => {
    const date = new Date("2021-01-15T12:00:00")
    expect(getRelativeTime(date)).toBe("2 years ago")
  })

  test("returns year ago", () => {
    const date = new Date("2022-01-15T12:00:00")
    expect(getRelativeTime(date)).toBe("1 year ago")
  })

  test("returns months ago", () => {
    const date = new Date("2023-11-15T12:00:00")
    expect(getRelativeTime(date)).toBe("2 months ago")
  })

  test("returns month ago", () => {
    const date = new Date("2023-12-15T12:00:00")
    expect(getRelativeTime(date)).toBe("1 month ago")
  })

  test("returns weeks ago", () => {
    const date = new Date("2024-01-01T12:00:00")
    expect(getRelativeTime(date)).toBe("2 weeks ago")
  })

  test("returns week ago", () => {
    const date = new Date("2024-01-08T12:00:00")
    expect(getRelativeTime(date)).toBe("1 week ago")
  })

  test("returns days ago", () => {
    const date = new Date("2024-01-13T12:00:00")
    expect(getRelativeTime(date)).toBe("2 days ago")
  })

  test("returns day ago", () => {
    const date = new Date("2024-01-14T12:00:00")
    expect(getRelativeTime(date)).toBe("1 day ago")
  })

  test("returns hours ago", () => {
    const date = new Date("2024-01-15T10:00:00")
    expect(getRelativeTime(date)).toBe("2 hours ago")
  })

  test("returns hour ago", () => {
    const date = new Date("2024-01-15T11:00:00")
    expect(getRelativeTime(date)).toBe("1 hour ago")
  })

  test("returns minutes ago", () => {
    const date = new Date("2024-01-15T11:58:00")
    expect(getRelativeTime(date)).toBe("2 minutes ago")
  })

  test("returns minute ago", () => {
    const date = new Date("2024-01-15T11:59:00")
    expect(getRelativeTime(date)).toBe("1 minute ago")
  })

  test("returns just now", () => {
    const date = new Date("2024-01-15T12:00:00")
    expect(getRelativeTime(date)).toBe("just now")
  })
})
