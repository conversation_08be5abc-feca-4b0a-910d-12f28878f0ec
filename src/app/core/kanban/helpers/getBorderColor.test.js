import { getBorderColor, isInvalidDaysInPhase, isWithinDeadline } from "./getBorderColor"

describe("getBorderColor", () => {
  // Test default return when project or currentPhase is not provided
  it("returns default color when project is null or undefined", () => {
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(null, currentPhase)).toBe("#0c2d5f")
    expect(getBorderColor(undefined, currentPhase)).toBe("#0c2d5f")
  })

  it("returns default color when currentPhase is null or undefined", () => {
    const project = { daysSpentInPhase: 5 }

    expect(getBorderColor(project, null)).toBe("#0c2d5f")
    expect(getBorderColor(project, undefined)).toBe("#0c2d5f")
  })

  // Test when both project and currentPhase are undefined
  it("returns default color when both parameters are undefined", () => {
    expect(getBorderColor(undefined, undefined)).toBe("#0c2d5f")
  })

  // Test condition: daysInPhase < goal (blue border)
  it("returns blue border when days in phase is less than goal", () => {
    const project = { daysSpentInPhase: 8 }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#0c2d5f")
  })

  // Test condition: daysInPhase >= goal && daysInPhase < phaseDeadline (yellow border)
  it("returns yellow border when days in phase is greater than or equal to goal but less than deadline", () => {
    const project = { daysSpentInPhase: 12 }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#e1b153")
  })

  // Test edge case: daysInPhase exactly equals goal
  it("returns yellow border when days in phase exactly equals goal", () => {
    const project = { daysSpentInPhase: 10 }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#e1b153")
  })

  // Test condition: daysInPhase >= phaseDeadline (red border)
  it("returns red border when days in phase is greater than or equal to deadline", () => {
    const project = { daysSpentInPhase: 15 }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#CC2936")
  })

  // Test edge case: daysInPhase exactly equals phaseDeadline
  it("returns red border when days in phase exactly equals deadline", () => {
    const project = { daysSpentInPhase: 15 }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#CC2936")
  })

  // Updated test for missing daysSpentInPhase - now returns blue
  it("returns blue when daysSpentInPhase property is missing", () => {
    const project = {}
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#0c2d5f")
  })

  // New tests for null and NaN daysSpentInPhase - should return blue
  it("returns blue when daysSpentInPhase is null", () => {
    const project = { daysSpentInPhase: null }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#0c2d5f")
  })

  it("returns blue when daysSpentInPhase is NaN", () => {
    const project = { daysSpentInPhase: NaN }
    const currentPhase = { goal: 10, phaseDeadline: 15 }

    expect(getBorderColor(project, currentPhase)).toBe("#0c2d5f")
  })

  // Test with missing goal property
  it("handles missing goal property", () => {
    const project = { daysSpentInPhase: 5 }
    const currentPhase = { phaseDeadline: 15 }

    // The implementation returns blue when goal is missing
    expect(getBorderColor(project, currentPhase)).toBe("#0c2d5f")
  })

  it("handles missing phaseDeadline property", () => {
    const project = { daysSpentInPhase: 12 }
    const currentPhase = { goal: 10 }

    // The implementation returns yellow when days in phase is greater than goal and no deadline is set
    expect(getBorderColor(project, currentPhase)).toBe("#e1b153")
  })

  // Returns yellow when days in phase is greater than goal and no deadline is set
  it("returns yellow when days in phase is greater than goal and no deadline is set", () => {
    const project = { daysSpentInPhase: 12 }
    const currentPhase = { goal: 10, phaseDeadline: undefined }

    expect(getBorderColor(project, currentPhase)).toBe("#e1b153")
  })
})

// Direct tests for helper functions
describe("isInvalidDaysInPhase", () => {
  it("returns true for undefined days", () => {
    expect(isInvalidDaysInPhase(undefined)).toBe(true)
  })

  it("returns true for null days", () => {
    expect(isInvalidDaysInPhase(null)).toBe(true)
  })

  it("returns true for NaN days", () => {
    expect(isInvalidDaysInPhase(NaN)).toBe(true)
  })

  it("returns false for valid number of days", () => {
    expect(isInvalidDaysInPhase(5)).toBe(false)
    expect(isInvalidDaysInPhase(0)).toBe(false)
  })
})

describe("isWithinDeadline", () => {
  it("returns true when days in phase is between goal and deadline", () => {
    expect(isWithinDeadline(12, 10, 15)).toBe(true)
  })

  it("returns false when days in phase is less than goal", () => {
    expect(isWithinDeadline(8, 10, 15)).toBe(false)
  })

  it("returns false when days in phase is greater than or equal to deadline", () => {
    expect(isWithinDeadline(15, 10, 15)).toBe(false)
    expect(isWithinDeadline(16, 10, 15)).toBe(false)
  })

  it("returns true when days in phase is greater than or equal to goal and no deadline exists", () => {
    expect(isWithinDeadline(12, 10, undefined)).toBe(true)
  })
})
