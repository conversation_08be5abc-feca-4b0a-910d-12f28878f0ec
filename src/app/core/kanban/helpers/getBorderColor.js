/**
 * Determines the border color based on days spent in phase compared to goal and deadline.
 *
 * @param {Object} project - The project object containing days spent in phase
 * @param {Object} currentPhase - The current phase object with goal and deadline values
 * @returns {string} - Color code for the border
 */

export function getBorderColor(project, currentPhase) {
  if (!project || !currentPhase) return "#0c2d5f"

  const daysInPhase = project.daysSpentInPhase

  if (isInvalidDaysInPhase(daysInPhase)) {
    return "#0c2d5f"
  }

  const { goal, phaseDeadline } = currentPhase

  if (daysInPhase < goal || !goal) {
    return "#0c2d5f"
  }

  if (isWithinDeadline(daysInPhase, goal, phaseDeadline)) {
    return "#e1b153"
  }

  return "#CC2936"
}

export function isInvalidDaysInPhase(daysInPhase) {
  return daysInPhase === undefined || daysInPhase === null || isNaN(daysInPhase)
}

export function isWithinDeadline(daysInPhase, goal, phaseDeadline) {
  return (daysInPhase >= goal && daysInPhase < phaseDeadline) || (daysInPhase >= goal && !phaseDeadline)
}
