const analyzePhases = (phaseHistory, phaseList, project) => {
  // Early return default structure instead of null
  if (!phaseList?.length) {
    return {
      highestValue: 0,
      data: [],
      maxTimeInPhase: 0,
      maxAverage: 0,
      maxGoal: 0,
      maxDeadline: 0
    }
  }

  // Initialize with empty arrays if they're not provided
  phaseHistory = phaseHistory || []

  const phaseDurations = new Map()

  // Process historical phases
  phaseHistory.forEach((change) => {
    const phaseId = change.fromPhaseId
    const daysSpent = change.totalDaysSpent || 0

    if (phaseDurations.has(phaseId)) {
      phaseDurations.set(phaseId, phaseDurations.get(phaseId) + daysSpent)
    } else {
      phaseDurations.set(phaseId, daysSpent)
    }
  })

  // Add current phase data from project
  if (project?.phaseId) {
    const currentPhaseId = project.phaseId.native
    const currentDaysSpent = project.daysSpentInPhase || 0

    if (phaseDurations.has(currentPhaseId)) {
      phaseDurations.set(currentPhaseId, phaseDurations.get(currentPhaseId) + currentDaysSpent)
    } else {
      phaseDurations.set(currentPhaseId, currentDaysSpent)
    }
  }

  const processedData = phaseList.map((phase) => {
    const nativePhaseId = phase.phaseId?.native || phase.id
    return {
      phaseName: phase.name,
      phaseId: nativePhaseId,
      timeInPhase: phaseDurations.get(nativePhaseId) || 0,
      average: phase.averageDays || 0,
      goal: phase.goal || 0,
      phaseDeadline: phase.phaseDeadline || 0
    }
  })

  const maxTimeInPhase = Math.max(...processedData.map((phase) => phase.timeInPhase), 0)
  const maxAverage = Math.max(...processedData.map((phase) => phase.average), 0)
  const maxGoal = Math.max(...processedData.map((phase) => phase.goal), 0)
  const maxDeadline = Math.max(...processedData.map((phase) => phase.phaseDeadline), 0)

  return {
    highestValue: Math.ceil(Math.max(maxTimeInPhase, maxAverage, maxGoal, maxDeadline)),
    data: processedData,
    maxTimeInPhase,
    maxAverage,
    maxGoal,
    maxDeadline
  }
}

export default analyzePhases
