import trimTitle from "./trimTitle"

describe("trimTitle", () => {
  const numberOfCharacters = 35
  const ellipsisLength = 3
  test("returns original title when shorter than 35 characters", () => {
    const shortTitle = "Short Title"
    expect(trimTitle(shortTitle)).toBe(shortTitle)
  })

  test("returns original title when exactly 35 characters", () => {
    const exactTitle = "This title has exactly thirty-five "
    // Verify the length before using it in the test
    expect(exactTitle.length).toBe(numberOfCharacters)
    expect(trimTitle(exactTitle)).toBe(exactTitle)
  })

  test("trims title and adds ellipsis when longer than 35 characters", () => {
    const longTitle =
      "This is a very long title that should definitely get trimmed because it exceeds the character limit"
    const expectedTrimmed = "This is a very long title that shou..."

    expect(trimTitle(longTitle)).toBe(expectedTrimmed)
    // 35 characters + 3 for ellipsis
    expect(trimTitle(longTitle).length).toBe(numberOfCharacters + ellipsisLength)
  })

  test("trims at exactly 35 characters before adding ellipsis", () => {
    const longTitle = "This title is exactly 36 characters!!!"
    const expectedTrimmed = "This title is exactly 36 characters..."

    expect(trimTitle(longTitle)).toBe(expectedTrimmed)
  })

  test("handles empty string", () => {
    expect(trimTitle("")).toBe("")
  })
})
