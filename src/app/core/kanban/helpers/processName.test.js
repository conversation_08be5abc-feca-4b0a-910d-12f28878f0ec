import processName from "./processName"

describe("processName", () => {
  test("formats full name to first name and last initial", () => {
    expect(processName("<PERSON>")).toBe("<PERSON>")
  })

  test("handles single word name", () => {
    expect(processName("John")).toBe("<PERSON>")
  })

  test("handles empty string", () => {
    expect(processName("")).toBe("")
  })

  test("handles null input", () => {
    expect(processName(null)).toBe("")
  })

  test("handles undefined input", () => {
    expect(processName(undefined)).toBe("")
  })

  test("handles multiple word names", () => {
    expect(processName("James Robert Smith")).toBe("James S.")
  })
})
