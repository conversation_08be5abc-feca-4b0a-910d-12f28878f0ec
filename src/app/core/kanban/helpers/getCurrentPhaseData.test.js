import getCurrentPhaseData from "./getCurrentPhaseData"

describe("getCurrentPhaseData", () => {
  // Sample phase list for testing
  const mockPhaseList = [
    { name: "Planning", phaseId: { native: 1 } },
    { name: "Development", phaseId: { native: 2 } },
    { name: "Testing", phaseId: { native: 3 } }
  ]

  // Test when both parameters are provided correctly
  it("returns the correct phase when project and phaseList are valid", () => {
    const project = { phaseId: { native: 2 } }

    const result = getCurrentPhaseData(project, mockPhaseList)

    expect(result).toEqual({ name: "Development", phaseId: { native: 2 } })
  })

  // Test when project is null or undefined
  it("returns null when project is null", () => {
    const result = getCurrentPhaseData(null, mockPhaseList)

    expect(result).toBeNull()
  })

  it("returns null when project is undefined", () => {
    const result = getCurrentPhaseData(undefined, mockPhaseList)

    expect(result).toBeNull()
  })

  // Test when phaseList is null or undefined
  it("returns null when phaseList is null", () => {
    const project = { phaseId: { native: 2 } }

    const result = getCurrentPhaseData(project, null)

    expect(result).toBeNull()
  })

  it("returns null when phaseList is undefined", () => {
    const project = { phaseId: { native: 2 } }

    const result = getCurrentPhaseData(project, undefined)

    expect(result).toBeNull()
  })

  // Test when both parameters are null or undefined
  it("returns null when both project and phaseList are null", () => {
    const result = getCurrentPhaseData(null, null)

    expect(result).toBeNull()
  })

  // Test when project has an unknown phase ID
  it("returns null when project phase ID is not found in phaseList", () => {
    const project = { phaseId: { native: 999 } }

    const result = getCurrentPhaseData(project, mockPhaseList)

    expect(result).toBeNull()
  })

  // Test with empty phaseList
  it("returns null when phaseList is empty", () => {
    const project = { phaseId: { native: 1 } }

    const result = getCurrentPhaseData(project, [])

    expect(result).toBeNull()
  })

  // Test with missing phaseId in project
  it("returns null when project is missing phaseId property", () => {
    const project = { name: "Project without phaseId" }

    const result = getCurrentPhaseData(project, mockPhaseList)

    expect(result).toBeNull()
  })

  // Test with malformed phaseId in project (missing native property)
  it("returns null when project phaseId is missing native property", () => {
    const project = { phaseId: { id: 2 } }

    const result = getCurrentPhaseData(project, mockPhaseList)

    expect(result).toBeNull()
  })
})
