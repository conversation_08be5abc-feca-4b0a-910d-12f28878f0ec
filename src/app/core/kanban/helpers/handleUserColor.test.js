import handleUserColor from "./handleUserColor"
import { earthyPastelColors } from "@/helpers/earthColors"

describe("handleUserColor", () => {
  beforeEach(() => {
    localStorage.clear()
  })

  test("returns existing user color from localStorage", () => {
    const testUser = { name: "<PERSON>", color: "#D7DDCF" }
    localStorage.setItem("userColors", JSON.stringify([testUser]))

    const result = handleUserColor("John Doe")
    expect(result).toEqual(testUser)
  })

  test("creates new user color when user doesn't exist", () => {
    const result = handleUserColor("<PERSON>")

    expect(result.name).toBe("Jane D.")
    expect(earthyPastelColors).toContain(result.color)
  })

  test("stores new user color in localStorage", () => {
    const userName = "Test User"
    const result = handleUserColor(userName)

    const storedColors = JSON.parse(localStorage.getItem("userColors"))
    expect(storedColors).toHaveLength(1)
    expect(storedColors[0]).toEqual(result)
  })

  test("handles empty localStorage", () => {
    const result = handleUserColor("New User")

    expect(result.name).toBe("New U.")
    expect(earthyPastelColors).toContain(result.color)
  })

  test("adds new user to existing users list", () => {
    const existingUser = { name: "Existing U.", color: "#D7DDCF" }
    localStorage.setItem("userColors", JSON.stringify([existingUser]))

    const result = handleUserColor("New User")

    const storedColors = JSON.parse(localStorage.getItem("userColors"))
    expect(storedColors).toHaveLength(2)
    expect(storedColors).toContainEqual(existingUser)
    expect(storedColors).toContainEqual(result)
  })
})
