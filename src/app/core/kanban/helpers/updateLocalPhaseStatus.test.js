import updateLocalPhaseStatus from "./updateLocalPhaseStatus"

describe("updateLocalPhaseStatus", () => {
  test("moves project from one status to another", () => {
    const sourceProject = { id: "project1", name: "Test Project" }
    const source = { status: "planning", index: 0 }
    const destination = { status: "inProgress", index: 1 }

    const sortedProjectList = {
      planning: {
        projectArray: [sourceProject, { id: "project2" }]
      },
      inProgress: {
        projectArray: [{ id: "project3" }, { id: "project4" }]
      }
    }

    const result = updateLocalPhaseStatus(sourceProject, source, destination, sortedProjectList)

    // Source array should have project removed
    expect(result.planning.projectArray).toHaveLength(1)
    expect(result.planning.projectArray[0].id).toBe("project2")

    // Destination array should have project inserted at index 1
    expect(result.inProgress.projectArray).toHaveLength(3)
    expect(result.inProgress.projectArray[1].id).toBe("project1")
  })

  test("rearranges project within the same status", () => {
    const sourceProject = { id: "project1", name: "Test Project" }
    const source = { status: "planning", index: 0 }
    const destination = { status: "planning", index: 2 }

    const sortedProjectList = {
      planning: {
        projectArray: [sourceProject, { id: "project2" }, { id: "project3" }]
      }
    }

    const result = updateLocalPhaseStatus(sourceProject, source, destination, sortedProjectList)

    // Array should be rearranged with project moved to the end
    expect(result.planning.projectArray).toHaveLength(3)
    expect(result.planning.projectArray[0].id).toBe("project2")
    expect(result.planning.projectArray[1].id).toBe("project3")
    expect(result.planning.projectArray[2].id).toBe("project1")
  })

  test("handles destination without explicit index (appends to end)", () => {
    const sourceProject = { id: "project1", name: "Test Project" }
    const source = { status: "planning", index: 0 }
    const destination = { status: "completed" }

    const sortedProjectList = {
      planning: {
        projectArray: [sourceProject, { id: "project2" }]
      },
      completed: {
        projectArray: [{ id: "project3" }]
      }
    }

    const result = updateLocalPhaseStatus(sourceProject, source, destination, sortedProjectList)

    // Source array should have project removed
    expect(result.planning.projectArray).toHaveLength(1)

    // Destination array should have project appended
    expect(result.completed.projectArray).toHaveLength(2)
    expect(result.completed.projectArray[1].id).toBe("project1")
  })

  test("handles empty destination array", () => {
    const sourceProject = { id: "project1", name: "Test Project" }
    const source = { status: "planning", index: 0 }
    const destination = { status: "completed", index: 0 }

    const sortedProjectList = {
      planning: {
        projectArray: [sourceProject]
      },
      completed: {
        projectArray: []
      }
    }

    const result = updateLocalPhaseStatus(sourceProject, source, destination, sortedProjectList)

    // Source array should be empty now
    expect(result.planning.projectArray).toHaveLength(0)

    // Destination array should have the project
    expect(result.completed.projectArray).toHaveLength(1)
    expect(result.completed.projectArray[0].id).toBe("project1")
  })

  test("preserves other columns that weren't involved in the move", () => {
    const sourceProject = { id: "project1", name: "Test Project" }
    const source = { status: "planning", index: 0 }
    const destination = { status: "inProgress", index: 0 }

    const sortedProjectList = {
      planning: {
        projectArray: [sourceProject]
      },
      inProgress: {
        projectArray: []
      },
      completed: {
        projectArray: [{ id: "project3" }]
      }
    }

    const result = updateLocalPhaseStatus(sourceProject, source, destination, sortedProjectList)

    // Verify untouched columns remain the same
    expect(result.completed.projectArray).toHaveLength(1)
    expect(result.completed.projectArray[0].id).toBe("project3")
  })
})
