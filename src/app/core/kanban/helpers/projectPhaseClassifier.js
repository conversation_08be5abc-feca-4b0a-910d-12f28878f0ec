export const projectPhaseClassifier = (
  projectList,
  phaseList,
  currentProjectPrimaryFilter,
  currentHashtagFilter
) => {
  // the Classifier provides:
  //   1. The list of primaries
  //   2. An object of phases where the key is the phaseId and the value is an object
  //      that contains the phase name, an array of projects, and a boolean that
  //      indicates whether the phase is being used
  let projectPrimary = currentProjectPrimaryFilter
  const phase = getPhases(phaseList)
  const primaryList = ["All Primaries", ...getPrimaryList(projectList)]
  const hashtagList = ["All Hashtags", ...getHashtagList(projectList)]

  let kanbanIndex = 0

  for (const project of projectList) {
    if (!projectPrimary) {
      projectPrimary = project.firstPrimaryName
    }

    // Checks that the project's phase is in the phase list and that the phase is active
    if (phase[project.phaseId.native]?.usePhase) {
      const passesPrimaryFilter =
        projectPrimary === "All Primaries" || project.firstPrimaryName === projectPrimary

      const passesHashtagFilter =
        !currentHashtagFilter ||
        currentHashtagFilter === "All Hashtags" ||
        (project?.hashtags.includes(currentHashtagFilter))

      // Only add the project if it passes both filters
      if (passesPrimaryFilter && passesHashtagFilter) {
        const phaseObject = phase[project.phaseId.native]
        project.index = kanbanIndex
        kanbanIndex++
        phaseObject.projectArray.push(project)
      }
    }
  }

  return { phase, primaryList, hashtagList }
}
const getPhases = (phaseList) => {
  // Transforms the phase list into an object with the phaseId as the key
  const phase = {}
  for (const element of phaseList) {
    phase[element.phaseId.native] = {
      projectArray: [],
      phaseName: element.name,
      usePhase: "usePhase" in element ? element.usePhase : true
    }
  }
  return phase
}

export const getPrimaryList = (projectList) => {
  // Extract all firstPrimaryUsernames
  // Remove any null or undefined values
  const primaries = projectList.map((project) => project.firstPrimaryName).filter(Boolean)

  // Remove duplicates using Set and convert back to array
  const uniquePrimaries = [...new Set(primaries)]

  // Sort alphabetically
  return uniquePrimaries.sort((a, b) => a.localeCompare(b))
}

export const getHashtagList = (projectList) => {
  // Extract all hashtags from all projects
  const allHashtags = projectList.reduce((acc, project) => {
    if (project.hashtags && Array.isArray(project.hashtags)) {
      acc.push(...project.hashtags)
    }
    return acc
  }, [])

  // Remove duplicates using Set and convert back to array
  const uniqueHashtags = [...new Set(allHashtags)]

  // Sort alphabetically
  return uniqueHashtags.sort((a, b) => a.localeCompare(b))
}
