import getPhaseBgColor from "./getPhaseBgColor"

describe("getPhaseBgColor", () => {
  test("returns empty string when usePhase is true", () => {
    const phase = {
      name: "Development",
      usePhase: true
    }

    expect(getPhaseBgColor(phase)).toBe("")
  })

  test("returns empty string when usePhase property is not present", () => {
    const phase = {
      name: "Development"
      // usePhase property not present
    }

    expect(getPhaseBgColor(phase)).toBe("")
  })

  test('returns "#E0E0DF" when usePhase is false and isPermanent is true', () => {
    const phase = {
      name: "Archived",
      usePhase: false,
      isPermanent: true
    }

    expect(getPhaseBgColor(phase)).toBe("#E0E0DF")
  })

  test('returns "#e9e9e9" when usePhase is false and isPermanent is false', () => {
    const phase = {
      name: "Development",
      usePhase: false,
      isPermanent: false
    }

    expect(getPhaseBgColor(phase)).toBe("#e9e9e9")
  })
})
