/**
 * Gets the current phase data for a project from a list of phases
 *
 * @param {Object} project - The project object containing phaseId information
 * @param {Array} phaseList - List of available phases
 * @returns {Object|null} - The current phase object or null if not found
 */
function getCurrentPhaseData(project, phaseList) {
  // Check if project or phaseList is missing
  if (!project || !phaseList) return null

  // Check if project has phaseId and phaseId.native
  if (!project.phaseId?.native) return null

  // Find the matching phase
  const currentPhase = phaseList.find((phase) => phase.phaseId.native === project.phaseId.native)

  return currentPhase || null
}

export default getCurrentPhaseData
