import React from "react"
import { render, screen, act, waitFor, fireEvent } from "@testing-library/react"
import Kan<PERSON> from "./page"
import * as firebaseFunctions from "firebase/functions"
import * as firebaseGetMethods from "../../../helpers/firebaseGetMethods"
import { KanbanContextProvider } from "./KanbanContext"

// Mock all required dependencies
jest.mock("firebase/functions", () => ({
  getFunctions: jest.fn(() => "mockedFunctions"),
  httpsCallable: jest.fn()
}))

jest.mock("../../../helpers/firebaseGetMethods", () => ({
  useProjectList: jest.fn(),
  useProjectTypeList: jest.fn(),
  useTenant: jest.fn(),
  useProjectPhaseList: jest.fn()
}))

jest.mock("./ProjectPhaseContent", () => {
  return function MockProjectPhaseContent() {
    return <div data-testid="project-phase-content">Project Phase Content</div>
  }
})

jest.mock("../settings/firebaseMethods.js/postMethods", () => ({
  useUpdateTenant: jest.fn().mockReturnValue({
    mutate: jest.fn(),
    isPending: false
  })
}))

jest.mock("./ProjectFilters", () => {
  const ProjectFilters = ({ setCurrentProjectPrimary, setCurrentHashtag, setProjectTypeId }) => {
    return (
      <div data-testid="project-filters">
        <button data-testid="change-primary" onClick={() => setCurrentProjectPrimary("John Doe")}>
          Change Primary
        </button>
        <button data-testid="change-hashtag" onClick={() => setCurrentHashtag("#frontend")}>
          Change Hashtag
        </button>
        <button data-testid="change-project-type" onClick={() => setProjectTypeId(2)}>
          Change Project Type
        </button>
      </div>
    )
  }
  ProjectFilters.displayName = "ProjectFilters"
  return ProjectFilters
})

jest.mock("../../../components/SplashScreen", () => {
  return function MockSplashScreen() {
    return <div data-testid="splash-screen">Loading...</div>
  }
})

// Mock AlertSnackbar component
jest.mock("../../../components/AlertSnackBar", () => {
  return function MockAlertSnackbar({ open, message, severity, onClose }) {
    return (
      <div data-testid="alert-snackbar" data-open={open} data-severity={severity} onClick={onClose}>
        {message}
      </div>
    )
  }
})

// Mock the projectPhaseClassifier with more complete data
jest.mock("./helpers/projectPhaseClassifier", () => ({
  projectPhaseClassifier: jest.fn().mockImplementation(() => ({
    phase: {
      1: { projectArray: [{ id: 1, name: "Test Project" }], phaseName: "Test Phase" },
      2: { projectArray: [], phaseName: "Another Phase" }
    },
    primaryList: ["All Primaries", "John Doe"],
    hashtagList: ["All Hashtags", "#test"]
  }))
}))

// Helper function to render Kanban with KanbanContextProvider
const renderKanbanWithContext = () => {
  return render(
    <KanbanContextProvider>
      <Kanban />
    </KanbanContextProvider>
  )
}

describe("Kanban Component", () => {
  const mockUpdateProject = jest.fn().mockResolvedValue({ data: { success: true } })
  const mockCreateNote = jest.fn().mockResolvedValue({ data: { success: true } })

  const mockProjectPhaseList = [
    { phaseId: { native: 1 }, name: "Planning", usePhase: true },
    { phaseId: { native: 2 }, name: "Development", usePhase: true }
  ]

  const mockProjectList = {
    projectData: [
      {
        phaseId: { native: 1 },
        firstPrimaryName: "John Doe",
        name: "Project A",
        hashtags: ["#frontend"]
      },
      {
        phaseId: { native: 2 },
        firstPrimaryName: "Jane Smith",
        name: "Project B",
        hashtags: ["#backend"]
      }
    ]
  }

  const mockTenantData = {
    userData: {
      tenant: "test-tenant",
      defaultProjectType: {
        "test-tenant": "1"
      }
    }
  }

  beforeEach(() => {
    jest.clearAllMocks()

    // Setup default mocks
    firebaseFunctions.httpsCallable.mockImplementation((_, functionName) => {
      if (functionName === "updateproject") return mockUpdateProject
      if (functionName === "createnote") return mockCreateNote
      return jest.fn()
    })

    // Default implementation for hooks with correct structure
    firebaseGetMethods.useProjectList.mockReturnValue({
      data: mockProjectList,
      isError: false,
      refetch: jest.fn()
    })

    firebaseGetMethods.useProjectTypeList.mockReturnValue({
      data: [{ projectTypeId: { native: 1 }, name: "Type 1" }],
      isLoading: false,
      isError: false
    })

    firebaseGetMethods.useTenant.mockReturnValue({
      data: mockTenantData,
      isLoading: false,
      isError: false
    })

    firebaseGetMethods.useProjectPhaseList.mockReturnValue({
      data: mockProjectPhaseList,
      isError: false
    })
  })

  test("renders splash screen when data is not loaded", async () => {
    // Empty project list to trigger splash screen
    firebaseGetMethods.useProjectTypeList.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false
    })

    renderKanbanWithContext()

    await waitFor(() => {
      expect(screen.getByTestId("splash-screen")).toBeInTheDocument()
    })
  })

  test("renders main content when data is loaded", async () => {
    renderKanbanWithContext()

    // Add a delay to allow state updates to process
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    await waitFor(() => {
      expect(screen.getByTestId("project-filters")).toBeInTheDocument()
      expect(screen.getByTestId("project-phase-content")).toBeInTheDocument()
    })
  })

  test("sets project type ID from tenant data", async () => {
    renderKanbanWithContext()

    // Add a delay to allow state updates to process
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    await waitFor(() => {
      // The first call might be with empty string or NaN during initialization
      // We're looking for a call with the correct value
      const calls = firebaseGetMethods.useProjectPhaseList.mock.calls
      const hasCorrectCall = calls.some((call) => call[0] === 1)
      expect(hasCorrectCall).toBe(true)
    })
  })

  test("shows alert snackbar when there is an error fetching projects", async () => {
    firebaseGetMethods.useProjectList.mockReturnValue({
      data: mockProjectList,
      isError: true,
      refetch: jest.fn()
    })

    renderKanbanWithContext()

    // Add a delay to allow error state to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // Wait for the alert snackbar to be in the document
    await waitFor(() => {
      const alertSnackbar = screen.getByTestId("alert-snackbar")
      expect(alertSnackbar).toBeInTheDocument()
      expect(alertSnackbar.getAttribute("data-open")).toBe("true")
      expect(alertSnackbar.getAttribute("data-severity")).toBe("error")
      expect(alertSnackbar).toHaveTextContent("Failed to load data")
    })
  })

  test("shows alert snackbar when there is an error fetching project phases", async () => {
    firebaseGetMethods.useProjectPhaseList.mockReturnValue({
      data: mockProjectPhaseList,
      isError: true
    })

    renderKanbanWithContext()

    // Add a delay to allow error state to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    await waitFor(() => {
      const alertSnackbar = screen.getByTestId("alert-snackbar")
      expect(alertSnackbar).toBeInTheDocument()
      expect(alertSnackbar.getAttribute("data-open")).toBe("true")
      expect(alertSnackbar.getAttribute("data-severity")).toBe("error")
    })
  })

  test("shows alert snackbar when there is an error fetching tenant data", async () => {
    firebaseGetMethods.useTenant.mockReturnValue({
      data: mockTenantData,
      isLoading: false,
      isError: true
    })

    renderKanbanWithContext()

    // Add a delay to allow error state to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    await waitFor(() => {
      const alertSnackbar = screen.getByTestId("alert-snackbar")
      expect(alertSnackbar).toBeInTheDocument()
      expect(alertSnackbar.getAttribute("data-open")).toBe("true")
      expect(alertSnackbar.getAttribute("data-severity")).toBe("error")
    })
  })

  test("shows alert snackbar when there is an error fetching project types", async () => {
    firebaseGetMethods.useProjectTypeList.mockReturnValue({
      data: [{ projectTypeId: { native: 1 }, name: "Type 1" }],
      isLoading: false,
      isError: true
    })

    renderKanbanWithContext()

    // Add a delay to allow error state to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    await waitFor(() => {
      const alertSnackbar = screen.getByTestId("alert-snackbar")
      expect(alertSnackbar).toBeInTheDocument()
      expect(alertSnackbar.getAttribute("data-open")).toBe("true")
      expect(alertSnackbar.getAttribute("data-severity")).toBe("error")
    })
  })

  test("refetches projects when project type ID changes", async () => {
    const mockRefetch = jest.fn()
    firebaseGetMethods.useProjectList.mockReturnValue({
      data: mockProjectList,
      isError: false,
      refetch: mockRefetch
    })

    renderKanbanWithContext()

    // Add a delay to allow initial render to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    const changeProjectTypeButton = screen.getByTestId("change-project-type")
    await act(async () => {
      fireEvent.click(changeProjectTypeButton)
    })

    await waitFor(() => {
      expect(mockRefetch).toHaveBeenCalled()
    })
  })

  test("updates current project primary when filter changes", async () => {
    renderKanbanWithContext()

    // Add a delay to allow initial render to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    const changePrimaryButton = screen.getByTestId("change-primary")
    await act(async () => {
      fireEvent.click(changePrimaryButton)
    })

    await waitFor(() => {
      // Verify that the useEffect for filter changes was triggered
      expect(firebaseGetMethods.useProjectList).toHaveBeenCalled()
    })
  })

  test("updates current hashtag when filter changes", async () => {
    renderKanbanWithContext()

    // Add a delay to allow initial render to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    const changeHashtagButton = screen.getByTestId("change-hashtag")
    await act(async () => {
      fireEvent.click(changeHashtagButton)
    })

    await waitFor(() => {
      // Verify that the useEffect for filter changes was triggered
      expect(firebaseGetMethods.useProjectList).toHaveBeenCalled()
    })
  })

  test("handles closing the alert snackbar", async () => {
    firebaseGetMethods.useProjectList.mockReturnValue({
      data: mockProjectList,
      isError: true,
      refetch: jest.fn()
    })

    renderKanbanWithContext()

    // Add a delay to allow error state to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100))
    })

    // First confirm alert snackbar is visible
    let alertSnackbar
    await waitFor(() => {
      alertSnackbar = screen.getByTestId("alert-snackbar")
      expect(alertSnackbar).toBeInTheDocument()
      expect(alertSnackbar.getAttribute("data-open")).toBe("true")
    })

    // Now click to close it
    await act(async () => {
      fireEvent.click(alertSnackbar)
    })

    // Check that it's now closed (the state should be updated)
    await waitFor(() => {
      expect(screen.getByTestId("alert-snackbar").getAttribute("data-open")).toBe("false")
    })
  })

  test("handles case when project phase list is empty", async () => {
    firebaseGetMethods.useProjectPhaseList.mockReturnValue({
      data: [],
      isError: false
    })

    renderKanbanWithContext()

    await waitFor(() => {
      expect(screen.getByTestId("splash-screen")).toBeInTheDocument()
    })
  })

  test("doesn't update filters when project data is missing", async () => {
    firebaseGetMethods.useProjectList.mockReturnValue({
      data: null,
      isError: false,
      refetch: jest.fn()
    })

    renderKanbanWithContext()

    await waitFor(() => {
      expect(screen.getByTestId("splash-screen")).toBeInTheDocument()
    })
  })
})
