import { Box, Typography, Modal } from "@mui/material"
import { bool, func, string } from "prop-types"

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  border: "2px solid red",
  boxShadow: 24,
  p: 4
}

function ErrorModal({ openModal, handleClose, errorMessage }) {
  return (
    <Modal
      open={openModal}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style}>
        <Typography id="modal-modal-title" variant="h4" component="h2">
          Error
        </Typography>
        <Typography id="modal-modal-title" variant="h6" component="h2">
          {errorMessage}
        </Typography>
      </Box>
    </Modal>
  )
}

export default ErrorModal

ErrorModal.propTypes = {
  openModal: bool.isRequired,
  handleClose: func.isRequired,
  errorMessage: string.isRequired
}
