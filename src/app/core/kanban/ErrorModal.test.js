import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import ErrorModal from "./ErrorModal"
import { Modal } from "@mui/material"

// Mock Material UI Modal component
jest.mock("@mui/material", () => {
  const actual = jest.requireActual("@mui/material")
  return {
    ...actual,
    Modal: jest.fn(({ children, open, onClose, ...props }) => {
      if (!open) return null
      return (
        <div data-testid="mock-modal" {...props}>
          <button data-testid="close-button" onClick={onClose}>
            Close
          </button>
          {children}
        </div>
      )
    })
  }
})

describe("ErrorModal", () => {
  const mockHandleClose = jest.fn()
  const mockErrorMessage = "This is an error message"

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders nothing when openModal is false", () => {
    render(<ErrorModal openModal={false} handleClose={mockHandleClose} errorMessage={mockErrorMessage} />)

    expect(screen.queryByTestId("mock-modal")).not.toBeInTheDocument()
    expect(screen.queryByText("Error")).not.toBeInTheDocument()
    expect(screen.queryByText(mockErrorMessage)).not.toBeInTheDocument()
  })

  it("renders modal with error title and message when openModal is true", () => {
    render(<ErrorModal openModal={true} handleClose={mockHandleClose} errorMessage={mockErrorMessage} />)

    expect(screen.getByTestId("mock-modal")).toBeInTheDocument()
    expect(screen.getByText("Error")).toBeInTheDocument()
    expect(screen.getByText(mockErrorMessage)).toBeInTheDocument()
  })

  it("calls handleClose when the modal is closed", () => {
    render(<ErrorModal openModal={true} handleClose={mockHandleClose} errorMessage={mockErrorMessage} />)

    const closeButton = screen.getByTestId("close-button")
    fireEvent.click(closeButton)

    expect(mockHandleClose).toHaveBeenCalledTimes(1)
  })

  it("passes correct props to Modal component", () => {
    render(<ErrorModal openModal={true} handleClose={mockHandleClose} errorMessage={mockErrorMessage} />)

    expect(Modal).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        onClose: mockHandleClose,
        "aria-labelledby": "modal-modal-title",
        "aria-describedby": "modal-modal-description"
      }),
      expect.anything()
    )
  })

  it("renders different error messages properly", () => {
    const newErrorMessage = "A different error occurred"

    const { rerender } = render(
      <ErrorModal openModal={true} handleClose={mockHandleClose} errorMessage={mockErrorMessage} />
    )

    expect(screen.getByText(mockErrorMessage)).toBeInTheDocument()

    // Rerender with different error message
    rerender(<ErrorModal openModal={true} handleClose={mockHandleClose} errorMessage={newErrorMessage} />)

    expect(screen.getByText(newErrorMessage)).toBeInTheDocument()
    expect(screen.queryByText(mockErrorMessage)).not.toBeInTheDocument()
  })
})
