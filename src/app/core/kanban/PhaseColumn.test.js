import React from "react"
import { render, screen } from "@testing-library/react"
import PhaseColumn from "./PhaseColumn"
import getPhaseBgColor from "./helpers/getPhaseBgColor"

// Mock dependencies
jest.mock("@hello-pangea/dnd", () => ({
  Droppable: jest.fn(({ children }) =>
    children(
      {
        innerRef: jest.fn(),
        droppableProps: {
          "data-testid": "droppable-props"
        },
        placeholder: <div data-testid="droppable-placeholder" />
      },
      {
        isDraggingOver: false
      }
    )
  )
}))

jest.mock("./ColumnHeader", () => {
  return jest.fn(({ phase, projects }) => (
    <div data-testid="column-header">
      <div>Phase: {phase.name}</div>
      <div>Project Count: {projects.projectArray.length}</div>
    </div>
  ))
})

jest.mock("./ProjectCard", () => {
  return jest.fn(({ project, position, createNote }) => (
    <div data-testid={`project-card-${position}`}>
      <div>Project: {project.projectName}</div>
      <button onClick={() => createNote({ projectId: project.projectId.native })}>Add Note</button>
    </div>
  ))
})

jest.mock("./helpers/getPhaseBgColor", () => jest.fn())

describe("PhaseColumn", () => {
  const mockPhase = {
    phaseId: { native: 123 },
    name: "Development",
    averageDays: 10,
    goal: 15,
    isPermanent: false
  }

  const mockProjects = {
    projectArray: [
      {
        links: {
          self: "link1",
          projectType: "type1",
          rootFolder: "folder1",
          client: "clientLink1",
          contacts: "contacts1"
        },
        phaseName: "Phase1",
        clientName: "Client A",
        firstPrimaryUsername: "user1",
        firstPrimaryName: "User One",
        hashtags: ["tag1", "tag2"],
        projectEmailAddress: "<EMAIL>",
        createdDate: "2023-01-01",
        projectId: { native: 1, partner: "p1" },
        projectTypeId: { native: 10, partner: "pt1" },
        projectName: "Project A",
        phaseId: { native: 123, partner: "ph1" },
        number: "PRJ001",
        projectUrl: "http://example.com/1",
        daysSpentInPhase: 5,
        formattedDate: "Jan 1, 2023",
        index: 0
      },
      {
        links: {
          self: "link2",
          projectType: "type2",
          rootFolder: "folder2",
          client: "clientLink2",
          contacts: "contacts2"
        },
        phaseName: "Phase1",
        clientName: "Client B",
        firstPrimaryUsername: "user2",
        firstPrimaryName: "User Two",
        hashtags: ["tag2", "tag3"],
        projectEmailAddress: "<EMAIL>",
        createdDate: "2023-01-02",
        projectId: { native: 2, partner: "p2" },
        projectTypeId: { native: 10, partner: "pt1" },
        projectName: "Project B",
        phaseId: { native: 123, partner: "ph1" },
        number: "PRJ002",
        projectUrl: "http://example.com/2",
        daysSpentInPhase: 3,
        formattedDate: "Jan 2, 2023",
        index: 1
      }
    ]
  }

  const mockCreateNote = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()

    // Default mock implementation
    getPhaseBgColor.mockReturnValue("#f5f5f5")
  })

  it("renders the column with header", () => {
    render(<PhaseColumn phase={mockPhase} projects={mockProjects} createNote={mockCreateNote} />)

    // Check if column header is rendered
    expect(screen.getByTestId("column-header")).toBeInTheDocument()
    expect(screen.getByText("Phase: Development")).toBeInTheDocument()
    expect(screen.getByText("Project Count: 2")).toBeInTheDocument()
  })

  it("renders project cards for each project", () => {
    render(<PhaseColumn phase={mockPhase} projects={mockProjects} createNote={mockCreateNote} />)

    // Check if project cards are rendered
    expect(screen.getByTestId("project-card-0")).toBeInTheDocument()
    expect(screen.getByTestId("project-card-1")).toBeInTheDocument()
    expect(screen.getByText("Project: Project A")).toBeInTheDocument()
    expect(screen.getByText("Project: Project B")).toBeInTheDocument()
  })

  it("handles empty project array", () => {
    const emptyProjects = { projectArray: [] }

    render(<PhaseColumn phase={mockPhase} projects={emptyProjects} createNote={mockCreateNote} />)

    // Check if column renders without project cards
    expect(screen.getByTestId("column-header")).toBeInTheDocument()
    expect(screen.getByText("Project Count: 0")).toBeInTheDocument()
    expect(screen.queryByTestId("project-card-0")).not.toBeInTheDocument()
  })

  it("sets up droppable context correctly", () => {
    render(<PhaseColumn phase={mockPhase} projects={mockProjects} createNote={mockCreateNote} />)

    // Check if droppable is set up correctly
    expect(screen.getByTestId("droppable-props")).toBeInTheDocument()
    expect(screen.getByTestId("droppable-placeholder")).toBeInTheDocument()
  })

  it("applies background color from getPhaseBgColor", () => {
    getPhaseBgColor.mockReturnValueOnce("#e0e0e0")

    render(<PhaseColumn phase={mockPhase} projects={mockProjects} createNote={mockCreateNote} />)

    // Check if helper was called with correct parameters
    expect(getPhaseBgColor).toHaveBeenCalledWith(mockPhase)
  })

  it("passes createNote function to ProjectCard", () => {
    const { getAllByText } = render(
      <PhaseColumn phase={mockPhase} projects={mockProjects} createNote={mockCreateNote} />
    )

    // Click "Add Note" button on the first project card
    const addNoteButtons = getAllByText("Add Note")
    addNoteButtons[0].click()

    // Check if createNote was called with correct parameters
    expect(mockCreateNote).toHaveBeenCalledWith({ projectId: 1 })
  })
})
