import React from "react"
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react"
import ProjectPhaseContent from "./ProjectPhaseContent"
import { useQueryClient } from "@tanstack/react-query"
import updateLocalPhaseStatus from "./helpers/updateLocalPhaseStatus"

// Mock dependencies
jest.mock("@tanstack/react-query", () => ({
  useQueryClient: jest.fn()
}))

jest.mock("./helpers/updateLocalPhaseStatus", () => jest.fn())

jest.mock("./PhaseColumn", () => {
  return jest.fn(({ phase, projects }) => (
    <div data-testid={`phase-column-${phase.phaseId.native}`}>
      <div>Phase: {phase.name}</div>
      <div>Project Count: {projects.projectArray.length}</div>
    </div>
  ))
})

// Mock AlertSnackbar
jest.mock("../../../components/AlertSnackBar", () => {
  return jest.fn(({ open, message, severity, onClose }) =>
    open ? (
      <div data-testid="alert-snackbar" data-severity={severity}>
        <div>Alert: {message}</div>
        <button onClick={onClose} data-testid="close-alert-button">
          Close
        </button>
      </div>
    ) : null
  )
})

// Mock DragDropContext
jest.mock("@hello-pangea/dnd", () => ({
  DragDropContext: jest.fn(({ children, onDragEnd }) => {
    // Store onDragEnd handler for tests to access
    window.mockOnDragEnd = onDragEnd
    return <div data-testid="drag-drop-context">{children}</div>
  })
}))

describe("ProjectPhaseContent", () => {
  // Mock data
  const mockPhaseList = [
    { phaseId: { native: 100 }, name: "Planning" },
    { phaseId: { native: 200 }, name: "Development" },
    { phaseId: { native: 300 }, name: "Testing" }
  ]

  // Mock project type list
  const mockProjectTypeList = [
    { projectTypeId: { native: "1" }, name: "Project Type 1" },
    { projectTypeId: { native: "2" }, name: "Project Type 2" }
  ]

  const mockSortedProjectList = {
    100: {
      projectArray: [
        {
          projectId: { native: 1 },
          projectName: "Project A",
          phaseId: { native: 100 }
        }
      ]
    },
    200: {
      projectArray: [
        {
          projectId: { native: 2 },
          projectName: "Project B",
          phaseId: { native: 200 }
        }
      ]
    },
    300: {
      projectArray: []
    }
  }

  const mockSetSortedProjectList = jest.fn()
  const mockUpdateProject = jest.fn()
  const mockCreateNote = jest.fn()

  // Mock QueryClient behavior
  const mockGetQueryData = jest.fn().mockImplementation((queryKey) => {
    if (queryKey[0] === "userList") {
      return { userData: { defaultProjectType: 1 } }
    }
    if (queryKey[0] === "projectTypeList") {
      return mockProjectTypeList
    }
    if (queryKey[0] === "projectPhaseList") {
      return mockPhaseList
    }
    return null
  })

  beforeEach(() => {
    jest.clearAllMocks()

    // Set up default mock implementations
    useQueryClient.mockReturnValue({
      getQueryData: mockGetQueryData
    })

    // Mock the update helper to return the updated list
    updateLocalPhaseStatus.mockImplementation((_sourceProject, _source, _destination, list) => {
      return { ...list }
    })
  })

  it("renders phase columns for each phase in the list", () => {
    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Check if columns are rendered for each phase
    expect(screen.getByTestId("phase-column-100")).toBeInTheDocument()
    expect(screen.getByTestId("phase-column-200")).toBeInTheDocument()
    expect(screen.getByTestId("phase-column-300")).toBeInTheDocument()

    // Check phase names
    expect(screen.getByText("Phase: Planning")).toBeInTheDocument()
    expect(screen.getByText("Phase: Development")).toBeInTheDocument()
    expect(screen.getByText("Phase: Testing")).toBeInTheDocument()

    // Check project counts
    expect(screen.getAllByText("Project Count: 1")).toHaveLength(2)
    expect(screen.getByText("Project Count: 0")).toBeInTheDocument()
  })

  it("does not render alert snackbar initially", () => {
    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    expect(screen.queryByTestId("alert-snackbar")).not.toBeInTheDocument()
  })

  it("handles drag end event when destination is same as source", async () => {
    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Call the onDragEnd handler with same source and destination
    const dragResult = {
      destination: { droppableId: "100", index: 0 },
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }

    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // Should not call updateLocalPhaseStatus or updateProject
    expect(updateLocalPhaseStatus).not.toHaveBeenCalled()
    expect(mockSetSortedProjectList).not.toHaveBeenCalled()
    expect(mockUpdateProject).not.toHaveBeenCalled()
  })

  it("handles drag end event when destination is null", async () => {
    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Call the onDragEnd handler with null destination
    const dragResult = {
      destination: null,
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }

    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // Should not call updateLocalPhaseStatus or updateProject
    expect(updateLocalPhaseStatus).not.toHaveBeenCalled()
    expect(mockSetSortedProjectList).not.toHaveBeenCalled()
    expect(mockUpdateProject).not.toHaveBeenCalled()
  })

  it("updates local state and calls API when project is moved to different phase", async () => {
    mockUpdateProject.mockResolvedValueOnce({})

    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Setup the mock return value for updateLocalPhaseStatus
    const updatedProjectList = { ...mockSortedProjectList }
    updateLocalPhaseStatus.mockReturnValueOnce(updatedProjectList)

    // Call the onDragEnd handler with different source and destination
    const dragResult = {
      destination: { droppableId: "200", index: 1 },
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }

    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // Should call updateLocalPhaseStatus
    expect(updateLocalPhaseStatus).toHaveBeenCalledWith(
      mockSortedProjectList["100"].projectArray[0],
      { status: "100", index: 0 },
      { status: "200", index: 1 },
      mockSortedProjectList
    )

    // Should update local state
    expect(mockSetSortedProjectList).toHaveBeenCalledWith(updatedProjectList)

    // Should call updateProject API
    expect(mockUpdateProject).toHaveBeenCalledWith({
      projectId: "1",
      newPhase: {
        phaseId: { native: "200" }
      }
    })

    // Should show success alert
    await waitFor(() => {
      expect(screen.getByTestId("alert-snackbar")).toBeInTheDocument()
      expect(screen.getByTestId("alert-snackbar")).toHaveAttribute("data-severity", "success")
      expect(screen.getByText(/Project phase successfully updated/)).toBeInTheDocument()
    })
  })

  it("shows error alert when updating project phase fails", async () => {
    mockUpdateProject.mockRejectedValueOnce(new Error("API error"))

    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Call the onDragEnd handler with different source and destination
    const dragResult = {
      destination: { droppableId: "200", index: 1 },
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }

    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // Wait for error alert to appear
    await waitFor(() => {
      expect(screen.getByTestId("alert-snackbar")).toBeInTheDocument()
      expect(screen.getByTestId("alert-snackbar")).toHaveAttribute("data-severity", "error")
      expect(
        screen.getByText(/Error updating project phase, please refresh window and try again/)
      ).toBeInTheDocument()
    })
  })

  it("closes alert snackbar when close button is clicked", async () => {
    mockUpdateProject.mockRejectedValueOnce(new Error("API error"))

    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Trigger an error
    const dragResult = {
      destination: { droppableId: "200", index: 1 },
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }
    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // Wait for alert to appear
    await waitFor(() => {
      expect(screen.getByTestId("alert-snackbar")).toBeInTheDocument()
    })

    // Click close button
    fireEvent.click(screen.getByTestId("close-alert-button"))

    // Alert should be closed
    expect(screen.queryByTestId("alert-snackbar")).not.toBeInTheDocument()
  })

  it("doesn't call API when project is moved within the same phase", async () => {
    render(
      <ProjectPhaseContent
        sortedProjectList={mockSortedProjectList}
        setSortedProjectList={mockSetSortedProjectList}
        updateProject={mockUpdateProject}
        createNote={mockCreateNote}
        projectTypeId="1"
      />
    )

    // Setup the mock return value for updateLocalPhaseStatus
    const updatedProjectList = { ...mockSortedProjectList }
    updateLocalPhaseStatus.mockReturnValueOnce(updatedProjectList)

    // Call the onDragEnd handler with same phase but different index
    const dragResult = {
      destination: { droppableId: "100", index: 1 },
      source: { droppableId: "100", index: 0 },
      draggableId: "1"
    }

    // Wrap in act to handle React state updates
    await act(async () => {
      window.mockOnDragEnd(dragResult)
    })

    // updateLocalPhaseStatus SHOULD be called to update local sorting
    expect(updateLocalPhaseStatus).toHaveBeenCalled()

    // Local state should be updated
    expect(mockSetSortedProjectList).toHaveBeenCalled()

    // Should NOT call updateProject API
    expect(mockUpdateProject).not.toHaveBeenCalled()

    // Should NOT show any alert
    expect(screen.queryByTestId("alert-snackbar")).not.toBeInTheDocument()
  })
})
