import { Stack, Typography } from "@mui/material"
import { shape, string, number, oneOfType, arrayOf, object, bool } from "prop-types"
import trimTitle from "./helpers/trimTitle"
import getPhaseInfo from "./helpers/getPhaseInfo"

const ColumnHeader = ({ phase, projects }) => {
  return (
    <Stack
      justifyContent="center"
      sx={{
        borderBottom: "solid 1px gray",
        marginBottom: "10px",
        padding: "10px 5px 10px 5px",
        height: "110px",
        backgroundColor: "#f3f3f3",
        position: "sticky",
        top: "64px",
        zIndex: 1
      }}
    >
      <Typography align="center" variant="subtitle1">
        {`${trimTitle(phase.name)} ${getPhaseInfo(phase, projects)}`}
      </Typography>
      <Typography align="center" variant="subtitle2">
        Goal: {phase.goal ? phase.goal + " days" : "-"}
      </Typography>
      <Typography align="center" variant="subtitle2">
        Average: {phase.averageDays ? phase.averageDays + " days" : "-"}
      </Typography>
    </Stack>
  )
}

export default ColumnHeader

ColumnHeader.propTypes = {
  phase: shape({
    averageDays: number,
    draggableId: string,
    goal: number,
    isPermanent: bool,
    links: object,
    name: string,
    phaseId: shape({ partner: oneOfType([string, number]), native: number })
  }).isRequired,
  projects: arrayOf(
    shape({
      links: shape({
        self: string,
        projectType: string,
        rootFolder: string,
        client: string,
        contacts: string
      }),
      phaseName: string,
      clientName: string,
      firstPrimaryUsername: string,
      firstPrimaryName: string,
      hashtags: arrayOf(string),
      projectEmailAddress: string,
      createdDate: string,
      projectId: shape({
        native: number,
        partner: oneOfType([string, number])
      }),
      projectTypeId: shape({
        native: number,
        partner: oneOfType([string, number])
      }),
      projectName: string,
      phaseId: shape({
        native: number,
        partner: oneOfType([string, number])
      }),
      number: string,
      projectUrl: string,
      daysSpentInPhase: number,
      formattedDate: string,
      index: number
    })
  ).isRequired
}
