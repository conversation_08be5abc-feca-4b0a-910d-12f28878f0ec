"use client"

import { useEffect, useState } from "react"
import { getFunctions, httpsCallable } from "firebase/functions"
import { Box, LinearProgress } from "@mui/material"

import ProjectPhaseContent from "./ProjectPhaseContent"
import ProjectFilters from "./ProjectFilters"

import {
  useProjectList,
  useProjectTypeList,
  useTenant,
  useProjectPhaseList
} from "@/helpers/firebaseGetMethods"
import { useUpdateTenant } from "../settings/firebaseMethods.js/postMethods"
import { projectPhaseClassifier } from "./helpers/projectPhaseClassifier"
import SplashScreen from "@/components/SplashScreen"
import AlertSnackbar from "@/components/AlertSnackBar"
import { useKanbanContext } from "./KanbanContext"

function Kanban() {
  const [sortedProjectList, setSortedProjectList] = useState({})
  const [projectPrimaryArray, setProjectPrimaryArray] = useState([])
  const [currentProjectPrimary, setCurrentProjectPrimary] = useState("All Primaries")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [projectHashtagArray, setProjectHashtagArray] = useState([])
  const [currentHashtag, setCurrentHashtag] = useState("")
  const { projectTypeId, setProjectTypeId } = useKanbanContext()

  const functions = getFunctions()
  const updateProject = httpsCallable(functions, "updateproject")
  const createNote = httpsCallable(functions, "createnote")

  const { data: projectList, isError: projectsError, refetch: refetchProjects } = useProjectList()

  const {
    data: projectTypeList,
    isLoading: loadingProjectTypes,
    isError: projectTypesError
  } = useProjectTypeList()

  const { data: tenantData, isLoading: isTenantLoading, isError: tenantError } = useTenant()

  const { data: projectPhaseList, isError: projectPhasesError } = useProjectPhaseList(projectTypeId, {
    // Don't retry on error to avoid multiple error messages
    retry: false,
    // Only run the query if we have a valid project type ID that exists in the list
    enabled:
      !!projectTypeId &&
      projectTypeList?.some((type) => String(type.projectTypeId.native) === String(projectTypeId))
  })

  const { mutate: updateTenant, isPending: isUpdatingUser } = useUpdateTenant()

  useEffect(
    function setDefaultTypeIfNotProvided() {
      if (
        !isTenantLoading &&
        projectTypeList?.length > 0 &&
        tenantData?.userData?.tenant &&
        !tenantData?.userData?.defaultProjectType
      ) {
        setAlertInfo({
          open: true,
          message: "We are setting a default project type for you. Please wait.",
          severity: "warning"
        })
        updateTenant(
          {
            defaultProjectType: {
              ...tenantData?.userData?.defaultProjectType,
              [tenantData?.userData?.tenant]: projectTypeList[0].projectTypeId.native
            },
            tenant: tenantData?.userData?.tenant
          },
          {
            onSuccess: () => {
              window.location.reload()
            },
            onError: (error) =>
              setAlertInfo({
                open: true,
                message: `Something went wrong. Please try again later, ${error.message}`,
                severity: "error"
              })
          }
        )
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [tenantData, projectTypeList, isTenantLoading, loadingProjectTypes]
  )

  useEffect(
    function updatesProjectTypeId() {
      if (tenantData?.userData?.defaultProjectType && projectTypeList?.length > 0) {
        const defaultId = tenantData.userData.defaultProjectType[tenantData.userData.tenant]

        // Check if the default project type exists in the current tenant's project type list
        const projectTypeExists = projectTypeList.some(
          (type) => String(type.projectTypeId.native) === String(defaultId)
        )

        if (projectTypeExists) {
          // If it exists, use it
          setProjectTypeId(Number(defaultId))
        } else {
          // If it doesn't exist, use the first available project type
          setProjectTypeId(Number(projectTypeList[0].projectTypeId.native))

          // Show a message to the user
          setAlertInfo({
            open: true,
            message:
              "The previously selected project type is not available in this tenant. Using the first available project type instead.",
            severity: "info"
          })
        }
      }
    },
    [tenantData, isTenantLoading, projectTypeList, setProjectTypeId]
  )

  useEffect(
    function handleErrors() {
      if (projectsError || projectPhasesError || tenantError || projectTypesError) {
        setAlertInfo({
          open: true,
          message: "Failed to load data. Please try again later.",
          severity: "error"
        })
      }
    },
    [projectsError, projectPhasesError, tenantError, projectTypesError]
  )

  useEffect(
    function handleProjectTypeChange() {
      refetchProjects()
    },
    [projectTypeId, refetchProjects]
  )

  useEffect(
    function handleFilterChange() {
      if (!projectTypeId || !projectList || !projectPhaseList || projectPhaseList.length === 0) {
        return
      }

      const organizedData = projectPhaseClassifier(
        projectList.projectData,
        projectPhaseList,
        currentProjectPrimary,
        currentHashtag
      )

      setSortedProjectList(organizedData.phase)

      // Update primary and hashtag arrays only when needed
      if (projectPrimaryArray.length < organizedData.primaryList.length) {
        setProjectPrimaryArray(organizedData.primaryList)
      }

      if (projectHashtagArray.length === 0 || projectHashtagArray.length < organizedData.hashtagList.length) {
        setProjectHashtagArray(organizedData.hashtagList)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [projectPhaseList, projectTypeId, currentProjectPrimary, currentHashtag, projectList]
  )

  const handleAlertClose = (_event) => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  if (Object.keys(sortedProjectList).length === 0 || projectTypeList.length === 0) {
    return <SplashScreen />
  }

  if (isUpdatingUser) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Box>
      <ProjectFilters
        currentProjectPrimary={currentProjectPrimary}
        setCurrentProjectPrimary={setCurrentProjectPrimary}
        setCurrentHashtag={setCurrentHashtag}
        currentHashtag={currentHashtag}
        projectPrimaryArray={projectPrimaryArray}
        projectHashtagArray={projectHashtagArray}
        projectTypeId={projectTypeId}
        setProjectTypeId={setProjectTypeId}
      />
      <ProjectPhaseContent
        sortedProjectList={sortedProjectList}
        projectTypeId={projectTypeId}
        setSortedProjectList={setSortedProjectList}
        updateProject={updateProject}
        createNote={createNote}
      />
      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
      />
    </Box>
  )
}
export default Kanban
