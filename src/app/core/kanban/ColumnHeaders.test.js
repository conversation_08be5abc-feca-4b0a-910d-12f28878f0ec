import React from "react"
import { render, screen } from "@testing-library/react"
import ColumnHeader from "./ColumnHeader"
import trimTitle from "./helpers/trimTitle"
import getPhaseInfo from "./helpers/getPhaseInfo"

// Mock the helper functions
jest.mock("./helpers/trimTitle", () => jest.fn((val) => val))
jest.mock("./helpers/getPhaseInfo", () => jest.fn(() => "(5)"))

describe("ColumnHeader", () => {
  const defaultPhase = {
    name: "Development Phase",
    phaseId: { native: 1, partner: "value" },
    goal: 10,
    averageDays: 8,
    isPermanent: false,
    draggableId: "phase-1",
    links: {}
  }

  // Changed from object with projectArray to an actual array to match propTypes
  const defaultProjects = [
    {
      id: "1",
      name: "Project 1",
      links: {
        self: "link1",
        projectType: "link2",
        rootFolder: "link3",
        client: "link4",
        contacts: "link5"
      },
      phaseName: "Development",
      clientName: "Client 1",
      firstPrimaryUsername: "user1",
      firstPrimaryName: "User One",
      hashtags: ["#tag1", "#tag2"],
      projectEmailAddress: "<EMAIL>",
      createdDate: "2023-01-01",
      projectId: {
        native: 1,
        partner: "value"
      },
      projectTypeId: {
        native: 1,
        partner: "value"
      },
      projectName: "Project 1",
      phaseId: {
        native: 1,
        partner: "value"
      },
      number: "123",
      projectUrl: "http://example.com",
      daysSpentInPhase: 5,
      formattedDate: "01/01/2023",
      index: 0
    },
    {
      id: "2",
      name: "Project 2",
      links: {
        self: "link1",
        projectType: "link2",
        rootFolder: "link3",
        client: "link4",
        contacts: "link5"
      },
      phaseName: "Development",
      clientName: "Client 2",
      firstPrimaryUsername: "user2",
      firstPrimaryName: "User Two",
      hashtags: ["#tag1", "#tag3"],
      projectEmailAddress: "<EMAIL>",
      createdDate: "2023-01-02",
      projectId: {
        native: 2,
        partner: "value"
      },
      projectTypeId: {
        native: 1,
        partner: "value"
      },
      projectName: "Project 2",
      phaseId: {
        native: 1,
        partner: "value"
      },
      number: "124",
      projectUrl: "http://example.com/2",
      daysSpentInPhase: 3,
      formattedDate: "01/02/2023",
      index: 1
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test("renders phase name with info", () => {
    render(<ColumnHeader phase={defaultPhase} projects={defaultProjects} />)

    expect(trimTitle).toHaveBeenCalledWith("Development Phase")
    expect(getPhaseInfo).toHaveBeenCalledWith(defaultPhase, defaultProjects)

    expect(screen.getByText(/Development Phase \(5\)/)).toBeInTheDocument()
  })

  test("renders with goal when it exists", () => {
    render(<ColumnHeader phase={defaultPhase} projects={defaultProjects} />)
    expect(screen.getByText("Goal: 10 days")).toBeInTheDocument()
  })

  test("renders with averageDays when it exists", () => {
    render(<ColumnHeader phase={defaultPhase} projects={defaultProjects} />)
    expect(screen.getByText("Average: 8 days")).toBeInTheDocument()
  })

  test("renders with dash when goal does not exist", () => {
    const phaseWithoutGoal = { ...defaultPhase, goal: null }
    render(<ColumnHeader phase={phaseWithoutGoal} projects={defaultProjects} />)
    expect(screen.getByText("Goal: -")).toBeInTheDocument()
  })

  test("renders with dash when averageDays does not exist", () => {
    const phaseWithoutAverage = { ...defaultPhase, averageDays: null }
    render(<ColumnHeader phase={phaseWithoutAverage} projects={defaultProjects} />)
    expect(screen.getByText("Average: -")).toBeInTheDocument()
  })

  test("renders with dash for zero values due to falsy evaluation", () => {
    const phaseWithZeros = { ...defaultPhase, goal: 0, averageDays: 0 }
    render(<ColumnHeader phase={phaseWithZeros} projects={defaultProjects} />)
    expect(screen.getByText("Goal: -")).toBeInTheDocument()
    expect(screen.getByText("Average: -")).toBeInTheDocument()
  })

  test("applies correct styling", () => {
    const { container } = render(<ColumnHeader phase={defaultPhase} projects={defaultProjects} />)
    const stackElement = container.firstChild

    expect(stackElement).toHaveStyle({
      borderBottom: "solid 1px gray",
      backgroundColor: "#f3f3f3"
    })
  })
})
