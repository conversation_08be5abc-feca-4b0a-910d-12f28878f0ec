import { DragDropContext } from "@hello-pangea/dnd"
import { Typo<PERSON>, Stack } from "@mui/material"
import { useState, useEffect } from "react"
import { useQueryClient } from "@tanstack/react-query"
import { string, number, oneOfType, arrayOf, shape, func } from "prop-types"
import PhaseColumn from "./PhaseColumn"
import AlertSnackbar from "@/components/AlertSnackBar"

import updateLocalPhaseStatus from "./helpers/updateLocalPhaseStatus"

function ProjectPhaseContent({
  sortedProjectList,
  projectTypeId,
  setSortedProjectList,
  updateProject,
  createNote
}) {
  const [updatedProjects, setUpdatedProjects] = useState([])
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const queryClient = useQueryClient()

  // Get the project type list to validate the projectTypeId
  const projectTypeList = queryClient.getQueryData(["projectTypeList"])

  // Check if the projectTypeId exists in the current tenant's project type list
  const isValidProjectTypeId = projectTypeList?.some(
    (type) => String(type.projectTypeId.native) === String(projectTypeId)
  )

  // Only get the phase list if the project type ID is valid
  const projectPhaseList = isValidProjectTypeId
    ? queryClient.getQueryData(["projectPhaseList", { projectTypeId: String(projectTypeId) }])
    : []

  // Show a warning if the project type ID is invalid
  useEffect(() => {
    if (projectTypeList?.length > 0 && !isValidProjectTypeId) {
      setAlertInfo({
        open: true,
        message:
          "The selected project type is not available in this tenant. Please select a different project type.",
        severity: "warning"
      })
    }
  }, [projectTypeId, projectTypeList, isValidProjectTypeId])

  // Check if all project arrays are empty
  const areAllProjectArraysEmpty = () => {
    if (!sortedProjectList) return true

    return Object.values(sortedProjectList).every(
      (phase) => !phase.projectArray || phase.projectArray.length === 0
    )
  }

  const onDragEnd = async (result) => {
    const { destination, source } = result

    if (
      !destination ||
      (destination.droppableId === source.droppableId && destination.index === source.index)
    ) {
      return
    }

    // Update the local state with the new order
    const updatedPhaseOrder = updateLocalPhaseStatus(
      sortedProjectList[source.droppableId].projectArray[source.index],
      { status: source.droppableId, index: source.index },
      { status: destination.droppableId, index: destination.index },
      sortedProjectList
    )

    setSortedProjectList(updatedPhaseOrder)

    // Only make API call if project moved to a different phase
    if (destination.droppableId !== source.droppableId) {
      setUpdatedProjects([...updatedProjects, result.draggableId])

      const updateData = {
        projectId: result.draggableId,
        newPhase: {
          phaseId: { native: destination.droppableId }
        }
      }

      try {
        await updateProject(updateData)
        setAlertInfo({
          open: true,
          message: `Project phase successfully updated`,
          severity: "success"
        })
      } catch (err) {
        setAlertInfo({
          open: true,
          message: `Error updating project phase, please refresh window and try again`,
          severity: "error"
        })
      }
    }
  }

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Stack direction="column">
        <Stack
          direction="row"
          sx={{
            bgcolor: "#F3f3f3",
            padding: "5px"
          }}
        >
          {projectPhaseList?.map(
            (phase) =>
              sortedProjectList?.[phase.phaseId.native] && (
                <PhaseColumn
                  key={phase.phaseId.native}
                  phase={phase}
                  projects={sortedProjectList[phase.phaseId.native]}
                  createNote={createNote}
                  updatedProjects={updatedProjects}
                />
              )
          )}
        </Stack>
        {areAllProjectArraysEmpty() && <NoProjectsPlaceholder />}
      </Stack>
      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
      />
    </DragDropContext>
  )
}

export default ProjectPhaseContent

ProjectPhaseContent.propTypes = {
  sortedProjectList: shape({
    links: shape({
      self: string,
      projectType: string,
      rootFolder: string,
      client: string,
      contacts: string
    }),
    phaseName: string,
    clientName: string,
    firstPrimaryUsername: string,
    firstPrimaryName: string,
    hashtags: arrayOf(string),
    projectEmailAddress: string,
    createdDate: string,
    projectId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectTypeId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    projectName: string,
    phaseId: shape({
      native: number,
      partner: oneOfType([string, number])
    }),
    number: string,
    projectUrl: string,
    daysSpentInPhase: number,
    formattedDate: string,
    index: number
  }).isRequired,
  projectTypeId: string.isRequired,
  setSortedProjectList: func,
  updateProject: func,
  createNote: func
}

const NoProjectsPlaceholder = () => {
  return (
    <Stack
      direction="row"
      justifyContent="space-around"
      alignItems="center"
      height="100%"
      width="100%"
      sx={{
        padding: "40px",
        bgcolor: "#F3f3f3"
      }}
    >
      <Typography
        variant="h5"
        color="text.secondary"
        sx={{
          textAlign: "center",
          opacity: 0.7
        }}
      >
        No projects found
      </Typography>
      <Typography
        variant="h5"
        color="text.secondary"
        sx={{
          textAlign: "center",
          opacity: 0.7
        }}
      >
        No projects found
      </Typography>
    </Stack>
  )
}
