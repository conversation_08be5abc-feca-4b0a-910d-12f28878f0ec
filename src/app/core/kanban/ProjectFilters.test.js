import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import ProjectFilters from "./ProjectFilters"
import { useQueryClient } from "@tanstack/react-query"

// Mock the hooks
jest.mock("@tanstack/react-query", () => ({
  useQueryClient: jest.fn()
}))

describe("ProjectFilters", () => {
  // Define mock props - changed projectTypeId to a number
  const mockProps = {
    currentProjectPrimary: "John Doe",
    setCurrentProjectPrimary: jest.fn(),
    setCurrentHashtag: jest.fn(),
    currentHashtag: "#design",
    projectPrimaryArray: ["All Primaries", "<PERSON>", "<PERSON>"],
    projectHashtagArray: ["All Hashtags", "#design", "#urgent"],
    projectTypeId: 1,
    setProjectTypeId: jest.fn()
  }

  // Updated mock to use numbers instead of strings for native IDs
  const mockProjectTypeList = [
    { projectTypeId: { native: 1 }, name: "Web Development" },
    { projectTypeId: { native: 2 }, name: "Mobile App" }
  ]

  // Mock queryClient.getQueryData
  const mockGetQueryData = jest.fn(() => mockProjectTypeList)

  beforeEach(() => {
    // Setup mocks
    useQueryClient.mockReturnValue({
      getQueryData: mockGetQueryData
    })

    // Clear mocks between tests
    jest.clearAllMocks()
  })

  it("renders all filter sections with correct titles", () => {
    render(<ProjectFilters {...mockProps} />)

    expect(screen.getByText("Project Type")).toBeInTheDocument()
    expect(screen.getByText("Project Primary")).toBeInTheDocument()
    expect(screen.getByText("Hashtag")).toBeInTheDocument()
  })

  it("renders project type dropdown with values from query client", () => {
    render(<ProjectFilters {...mockProps} />)

    // Check if getQueryData was called correctly
    expect(mockGetQueryData).toHaveBeenCalledWith(["projectTypeList"])

    // Check if options are rendered
    expect(screen.getByText("Web Development")).toBeInTheDocument()
    expect(screen.getByText("Mobile App")).toBeInTheDocument()
  })

  it("renders primary dropdown with values from props", () => {
    render(<ProjectFilters {...mockProps} />)

    mockProps.projectPrimaryArray.forEach((primary) => {
      expect(screen.getByText(primary)).toBeInTheDocument()
    })
  })

  it("renders hashtag dropdown with values from props", () => {
    render(<ProjectFilters {...mockProps} />)

    mockProps.projectHashtagArray.forEach((hashtag) => {
      expect(screen.getByText(hashtag)).toBeInTheDocument()
    })
  })

  it("calls setProjectTypeId when project type is changed", () => {
    render(<ProjectFilters {...mockProps} />)

    // Find the select element and change its value
    const projectTypeSelect = screen.getAllByRole("combobox")[0]
    fireEvent.change(projectTypeSelect, { target: { value: "2" } })

    expect(mockProps.setProjectTypeId).toHaveBeenCalledWith("2")
  })

  it("calls setCurrentProjectPrimary when primary is changed", () => {
    render(<ProjectFilters {...mockProps} />)

    // Find the select element and change its value
    const primarySelect = screen.getAllByRole("combobox")[1]
    fireEvent.change(primarySelect, { target: { value: "Jane Smith" } })

    expect(mockProps.setCurrentProjectPrimary).toHaveBeenCalledWith("Jane Smith")
  })

  it("calls setCurrentHashtag when hashtag is changed", () => {
    render(<ProjectFilters {...mockProps} />)

    // Find the select element and change its value
    const hashtagSelect = screen.getAllByRole("combobox")[2]
    fireEvent.change(hashtagSelect, { target: { value: "#urgent" } })

    expect(mockProps.setCurrentHashtag).toHaveBeenCalledWith("#urgent")
  })

  it("applies the current values to the dropdown selections", () => {
    render(<ProjectFilters {...mockProps} />)

    // Check if the current values are selected
    const [projectTypeSelect, primarySelect, hashtagSelect] = screen.getAllByRole("combobox")

    expect(projectTypeSelect.value).toBe("1")
    expect(primarySelect.value).toBe(mockProps.currentProjectPrimary)
    expect(hashtagSelect.value).toBe(mockProps.currentHashtag)
  })

  it("handles empty project type list gracefully", () => {
    // Mock an empty project type list
    mockGetQueryData.mockReturnValueOnce([])

    render(<ProjectFilters {...mockProps} />)

    // Should not throw an error and should render the component
    expect(screen.getByText("Project Type")).toBeInTheDocument()
  })
})
