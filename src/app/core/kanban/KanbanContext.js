import { createContext, useContext, useMemo, useState } from "react"
import { node } from "prop-types"

const KanbanContext = createContext(undefined)

export const KanbanContextProvider = ({ children }) => {
  const [projectTypeId, setProjectTypeId] = useState("")
  const value = useMemo(
    () => ({
      projectTypeId,
      setProjectTypeId
    }),
    [projectTypeId]
  )

  return <KanbanContext.Provider value={value}>{children}</KanbanContext.Provider>
}
KanbanContextProvider.propTypes = {
  children: node.isRequired
}

export const useKanbanContext = () => {
  const context = useContext(KanbanContext)

  if (context === undefined) {
    throw new Error("useKanbanContext must be used within a KanbanContextProvider")
  }

  return context
}
