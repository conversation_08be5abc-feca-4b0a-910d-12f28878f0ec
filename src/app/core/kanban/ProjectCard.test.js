import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import ProjectCard from "./ProjectCard"
import { useQueryClient } from "@tanstack/react-query"
import handleUserColor from "./helpers/handleUserColor"
import getCurrentPhaseData from "./helpers/getCurrentPhaseData"
import { getBorderColor } from "./helpers/getBorderColor"
import { KanbanContextProvider } from "./KanbanContext"

// Mock dependencies
jest.mock("@tanstack/react-query", () => ({
  useQueryClient: jest.fn()
}))

jest.mock("./helpers/handleUserColor", () => jest.fn())
jest.mock("./helpers/getCurrentPhaseData", () => jest.fn())
jest.mock("./helpers/getBorderColor", () => ({
  getBorderColor: jest.fn()
}))

// Mock ProjectModal
jest.mock("./ProjectModal/ProjectModal", () => {
  return jest.fn(({ openModal, handleClose, project, createNote }) =>
    openModal ? (
      <div data-testid="project-modal">
        <div>Modal Content for: {project.projectName}</div>
        <button onClick={handleClose} data-testid="close-modal-button">
          Close
        </button>
        <button onClick={() => createNote({ content: "Test note" })} data-testid="create-note-button">
          Create Note
        </button>
      </div>
    ) : null
  )
})

// Mock the Draggable component from hello-pangea/dnd
jest.mock("@hello-pangea/dnd", () => ({
  Draggable: jest.fn(({ children }) =>
    children(
      {
        draggableProps: {
          "data-testid": "draggable-props",
          style: {}
        },
        dragHandleProps: {
          "data-testid": "drag-handle-props"
        },
        innerRef: jest.fn()
      },
      {
        isDragging: false
      }
    )
  )
}))

// Helper function to render with KanbanContext
const renderWithKanbanContext = (ui) => {
  return render(<KanbanContextProvider>{ui}</KanbanContextProvider>)
}

describe("ProjectCard", () => {
  const mockProject = {
    projectId: { native: 123 },
    projectName: "Test Project",
    clientName: "Test Client",
    formattedDate: "01/01/2024",
    daysSpentInPhase: 5,
    firstPrimaryName: "John Doe",
    phaseId: { native: 1 }
  }

  const mockPhaseList = [{ phaseId: { native: 1 }, name: "Planning", goal: 10, phaseDeadline: 15 }]

  const mockCurrentPhase = { name: "Planning", goal: 10, phaseDeadline: 15 }

  const mockUserColor = { name: "John D.", color: "#123456" }

  const mockCreateNote = jest.fn()

  const mockGetQueryData = jest.fn().mockImplementation((queryKey) => {
    if (queryKey[0] === "userList") {
      return { userData: { defaultProjectType: 1 } }
    }
    if (queryKey[0] === "projectPhaseList") {
      return mockPhaseList
    }
    return null
  })

  beforeEach(() => {
    jest.clearAllMocks()

    // Set up default mock implementations
    useQueryClient.mockReturnValue({
      getQueryData: mockGetQueryData
    })

    handleUserColor.mockReturnValue(mockUserColor)
    getCurrentPhaseData.mockReturnValue(mockCurrentPhase)
    getBorderColor.mockReturnValue("#0c2d5f")
  })

  it("renders project data correctly", () => {
    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Check if main project information is displayed
    expect(screen.getByText("Test Project")).toBeInTheDocument()
    expect(screen.getByText("Test Client")).toBeInTheDocument()
    expect(screen.getByText("01/01/2024")).toBeInTheDocument()
    expect(screen.getByText("5 Days")).toBeInTheDocument()

    // Check if user chip is displayed
    expect(screen.getByText("John D.")).toBeInTheDocument()
  })

  it("handles project with missing days spent", () => {
    const projectWithoutDays = { ...mockProject, daysSpentInPhase: null }

    renderWithKanbanContext(
      <ProjectCard
        project={projectWithoutDays}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Should not display days text
    expect(screen.queryByText(/Days/)).not.toBeInTheDocument()
  })

  it("calls helper functions with correct parameters", () => {
    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Check if helpers were called correctly
    expect(handleUserColor).toHaveBeenCalledWith(mockProject.firstPrimaryName)
    expect(getCurrentPhaseData).toHaveBeenCalledWith(mockProject, mockPhaseList)
    expect(getBorderColor).toHaveBeenCalledWith(mockProject, mockCurrentPhase)
  })

  it("opens modal on card click", () => {
    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Initially, modal should not be visible
    expect(screen.queryByTestId("project-modal")).not.toBeInTheDocument()

    // Click on the card to open modal
    fireEvent.click(screen.getByText("Test Project"))

    // Modal should now be visible
    expect(screen.getByTestId("project-modal")).toBeInTheDocument()
    expect(screen.getByText("Modal Content for: Test Project")).toBeInTheDocument()
  })

  it("closes modal when close button is clicked", () => {
    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Open the modal
    fireEvent.click(screen.getByText("Test Project"))
    expect(screen.getByTestId("project-modal")).toBeInTheDocument()

    // Click close button
    fireEvent.click(screen.getByTestId("close-modal-button"))

    // Modal should be closed
    expect(screen.queryByTestId("project-modal")).not.toBeInTheDocument()
  })

  it("passes createNote to ProjectModal", () => {
    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Open the modal
    fireEvent.click(screen.getByText("Test Project"))

    // Click create note button
    fireEvent.click(screen.getByTestId("create-note-button"))

    // Check if createNote was called
    expect(mockCreateNote).toHaveBeenCalledWith({ content: "Test note" })
  })

  it("handles project without phase list", () => {
    // Mock missing phase list
    mockGetQueryData.mockImplementationOnce((queryKey) => {
      if (queryKey[0] === "userList") {
        return { userData: { defaultProjectType: 1 } }
      }
      return null
    })

    getCurrentPhaseData.mockReturnValueOnce(null)

    renderWithKanbanContext(
      <ProjectCard
        project={mockProject}
        position={0}
        createNote={mockCreateNote}
        onHeightChange={jest.fn()}
      />
    )

    // Component should render without errors
    expect(screen.getByText("Test Project")).toBeInTheDocument()
  })
})
