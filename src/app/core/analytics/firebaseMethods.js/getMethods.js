import { httpsCallable, getFunctions } from "firebase/functions"
import { useQuery } from "@tanstack/react-query"
import app from "@/app/firebase"

const functions = getFunctions(app)

export function useProjectAnalytics(projectTypeId, palette, queryOptions) {
  return useQuery({
    queryKey: ["projectAnalytics", { projectTypeId: String(projectTypeId) }],
    queryFn: async () => {
      const analyticsDataCaller = httpsCallable(functions, "getanalyticsdata")
      const response = await analyticsDataCaller({ projectTypeId, palette })
      if (!response.data) {
        throw new Error("Failed to fetch project analytics data")
      }
      return response.data
    },
    enabled: !!projectTypeId,
    ...queryOptions
  })
}

export function useBillingAnalytics(palette, queryOptions) {
  return useQuery({
    queryKey: ["billingAnalytics"],
    queryFn: async () => {
      const billingAnalyticsDataCaller = httpsCallable(functions, "getbillinganalyticsdata")
      const response = await billingAnalyticsDataCaller({ palette })
      if (!response.data) {
        throw new Error("Failed to fetch billing analytics data")
      }
      return response.data
    },
    ...queryOptions
  })
}

export function useInvoiceAnalytics(palette, queryOptions) {
  return useQuery({
    queryKey: ["invoiceAnalytics"],
    queryFn: async () => {
      const invoiceAnalyticsDataCaller = httpsCallable(functions, "getinvoiceanalyticsdata")
      const response = await invoiceAnalyticsDataCaller({ palette })
      if (!response.data) {
        throw new Error("Failed to fetch invoice analytics data")
      }
      return response.data
    },
    ...queryOptions
  })
}
