"use client"

import ProjectTypeDropdown from "./ProjectTypeDropdown"
// import PrimaryPerformance from "./PrimaryPerformance"
import ProjectAnalytics from "./ProjectAnalytics"
import BillingAnalytics from "./BillingAnalytics"
import InvoiceData from "./InvoiceData"

import { useState, useEffect } from "react"
import { Box, LinearProgress } from "@mui/material"
import { useProjectTypeList, useTenant } from "@/helpers/firebaseGetMethods"
import { useUpdateTenant } from "../settings/firebaseMethods.js/postMethods"
import AlertSnackbar from "@/components/AlertSnackBar"

function Analytics() {
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [projectTypeId, setProjectTypeId] = useState(null)
  const [selectedTab, setSelectedTab] = useState(0)

  const { data: projectTypeList, isLoading: loadingProjectTypes } = useProjectTypeList()
  const { data: tenantData, isLoading: isTenantLoading } = useTenant()
  const { mutate: updateTenant, isPending: isUpdatingUser } = useUpdateTenant()

  const isLoading = loadingProjectTypes || isTenantLoading

  useEffect(
    function setDefaultTypeIfNotProvided() {
      if (
        !isLoading &&
        projectTypeList?.length > 0 &&
        tenantData?.userData?.tenant &&
        !tenantData?.userData?.defaultProjectType[tenantData?.userData?.tenant]
      ) {
        setAlertInfo({
          open: true,
          message: "We are setting a default project type for you. Please wait.",
          severity: "warning"
        })
        updateTenant(
          {
            defaultProjectType: {
              ...tenantData?.userData?.defaultProjectType,
              [tenantData?.userData?.tenant]: projectTypeList[0].projectTypeId.native
            },
            tenant: tenantData?.userData?.tenant
          },
          {
            onSuccess: () => {
              window.location.reload()
            },
            onError: (error) =>
              setAlertInfo({
                open: true,
                message: `Something went wrong. Please try again later, ${error.message}`,
                severity: "error"
              })
          }
        )
      }
    },
    [tenantData, projectTypeList, loadingProjectTypes, isLoading, updateTenant]
  )

  useEffect(
    function getProjectTypeIdFromDB() {
      if (tenantData?.userData?.defaultProjectType && projectTypeList?.length > 0) {
        const defaultId = tenantData.userData.defaultProjectType[tenantData.userData.tenant]

        // Check if the default project type exists in the current tenant's project type list
        const projectTypeExists = projectTypeList.some(
          (type) => String(type.projectTypeId.native) === String(defaultId)
        )

        if (projectTypeExists) {
          // If it exists, use it
          setProjectTypeId(Number(defaultId))
        } else {
          // If it doesn't exist, use the first available project type
          setProjectTypeId(Number(projectTypeList[0].projectTypeId.native))

          // Show a message to the user
          setAlertInfo({
            open: true,
            message:
              "The previously selected project type is not available in this tenant. Using the first available project type instead.",
            severity: "info"
          })
        }
      }
    },
    [tenantData, projectTypeList]
  )

  const renderContent = () => {
    const defaultTab = 0
    const secondTab = 1
    const thirdTab = 2
    // const fourthTab = 3

    if (selectedTab === defaultTab) {
      return projectTypeId !== null ? (
        <ProjectAnalytics projectTypeId={projectTypeId} />
      ) : (
        <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
      )
    }
    if (selectedTab === secondTab) {
      return <BillingAnalytics />
    }
    if (selectedTab === thirdTab) {
      return <InvoiceData />
    }
    // if (selectedTab === fourthTab) {
    //   return <PrimaryPerformance />
    // }
    return null
  }

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  if (isUpdatingUser) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Box>
      <ProjectTypeDropdown
        selectedTab={selectedTab}
        setSelectedTab={setSelectedTab}
        projectTypeId={projectTypeId}
        setProjectTypeId={setProjectTypeId}
      />
      {renderContent()}
      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Box>
  )
}

export default Analytics
