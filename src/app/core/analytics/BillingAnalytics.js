import { useState, useEffect } from "react"
import { <PERSON>, Typo<PERSON>, Card, LinearProgress, Stack, Chip, NativeSelect } from "@mui/material"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/x-charts"
import { PALETTE } from "./helpers/constants"
import { useBillingAnalytics } from "./firebaseMethods.js/getMethods"

function BillingAnalytics() {
  const [chartMetrics, setChartMetrics] = useState()
  const [filteredMetrics, setFilteredMetrics] = useState()
  const [selectedUsers, setSelectedUsers] = useState([])

  const { data: billingAnalyticsData, isLoading: isBillingAnalyticsLoading } = useBillingAnalytics(PALETTE)

  // Filter data based on selected users
  useEffect(() => {
    if (!chartMetrics) return

    // If no users selected, show all data
    if (selectedUsers.length === 0) {
      setFilteredMetrics(chartMetrics)
      return
    }

    // Create a filtered copy of the data
    const filtered = { ...chartMetrics }

    // Filter user-specific data
    const userIndices = selectedUsers
      .map((user) => chartMetrics.billingUserArray.findIndex((u) => u === user))
      .filter((index) => index !== -1)

    // Filter user entry quantities (this week and last week)
    filtered.billingUserEntryQuantityThisWeek = userIndices.map(
      (index) => chartMetrics.billingUserEntryQuantityThisWeek[index]
    )
    filtered.billingUserEntryQuantityLastWeek = userIndices.map(
      (index) => chartMetrics.billingUserEntryQuantityLastWeek[index]
    )

    // Update billingUserArray to only show selected users
    filtered.billingUserArray = selectedUsers

    // For weekday data, we need to recalculate totals based on selected users
    // This is a simplification - in a real app, it is necesary to access the raw data
    // Here we're just scaling down proportionally
    if (selectedUsers.length < chartMetrics.billingUserArray.length) {
      const selectedUserTotal = filtered.billingUserEntryQuantityThisWeek.reduce((sum, val) => sum + val, 0)
      const allUserTotal = chartMetrics.billingUserEntryQuantityThisWeek.reduce((sum, val) => sum + val, 0)

      // Only scale if we have data to avoid division by zero
      if (allUserTotal > 0) {
        const ratio = selectedUserTotal / allUserTotal
        filtered.billingWeekdayQuantityThisWeek = chartMetrics.billingWeekdayQuantityThisWeek.map(
          (val) => val * ratio
        )
        filtered.billingWeekdayQuantityLastWeek = chartMetrics.billingWeekdayQuantityLastWeek.map(
          (val) => val * ratio
        )

        // Update time code stacks for weekday
        filtered.timeCodeStackWeekday = chartMetrics.timeCodeStackWeekday.map((stack) => ({
          ...stack,
          data: stack.data.map((val) => val * ratio)
        }))
      }
    }

    // Update time code stacks for user
    filtered.timeCodeStackUser = chartMetrics.timeCodeStackUser.map((stack) => ({
      ...stack,
      data: userIndices.map((index) => stack.data[index])
    }))

    // Update total hours
    filtered.totalHoursThisWeek = filtered.billingUserEntryQuantityThisWeek.reduce((sum, val) => sum + val, 0)

    // Note: For the pie chart (topProjectArray), we'd need additional data to filter by user
    // For now, we'll keep it as is, but in a real implementation it is necesary to filter this too

    setFilteredMetrics(filtered)
  }, [chartMetrics, selectedUsers])

  // Initialize data
  useEffect(() => {
    if (billingAnalyticsData) {
      setChartMetrics(billingAnalyticsData)
      setFilteredMetrics(billingAnalyticsData)
    }
  }, [billingAnalyticsData])

  const handleUserChange = (event) => {
    const value = event.target.value
    const newSelectedUsers = [...selectedUsers, value]
    setSelectedUsers([...new Set(newSelectedUsers)])
  }

  const handleDeleteUser = (userToDelete) => {
    setSelectedUsers(selectedUsers.filter((user) => user !== userToDelete))
  }

  if (isBillingAnalyticsLoading || !filteredMetrics) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <>
      <Stack
        direction="row"
        alignItems="center"
        sx={{
          padding: "30px 30px 30px 30px",
          position: "sticky",
          top: 60,
          zIndex: 10,
          backgroundColor: "#fff",
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
          width: "100%"
        }}
      >
        <Box sx={{ margin: "0 30px 0 0 " }}>
          <Typography variant="h6">Select Users</Typography>
          <NativeSelect id="user-filter" value={"Select users"} onChange={handleUserChange}>
            <option value={"Select users"}>Select users</option>
            {chartMetrics?.billingUserArray.map((user) => (
              <option key={user} value={user}>
                {user}
              </option>
            ))}
          </NativeSelect>
        </Box>

        {selectedUsers?.length !== 0 && (
          <Stack direction="row" gap={1}>
            {selectedUsers?.map((value) => (
              <Chip
                key={value}
                label={value}
                onDelete={() => handleDeleteUser(value)}
                onMouseDown={(event) => {
                  event.stopPropagation()
                }}
              />
            ))}
          </Stack>
        )}
      </Stack>
      <Stack
        direction="row"
        justifyContent="space-around"
        flexWrap="wrap"
        sx={{
          bgcolor: "#F3f3f3"
        }}
      >
        <Card
          sx={{
            marginTop: "50px",
            width: "45%",
            boxShadow: "4px 6px 6px #aaa"
          }}
        >
          <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
            Total Hours Per User This Week Vs Last Week
          </Typography>
          <BarChart
            margin={{
              left: 125,
              right: 50,
              top: 50,
              bottom: 50
            }}
            height={500}
            series={[
              {
                label: "This Week",
                color: "#e1b153",
                data: filteredMetrics.billingUserEntryQuantityThisWeek
              },
              {
                label: "Last Week",
                color: "#0c2d5f",
                data: filteredMetrics.billingUserEntryQuantityLastWeek
              }
            ]}
            layout="horizontal"
            grid={{ vertical: true }}
            slots={{ legend: () => null }}
            yAxis={[
              {
                data: filteredMetrics.billingUserArray,
                scaleType: "band"
              }
            ]}
          />
        </Card>

        <Card
          sx={{
            marginTop: "50px",
            width: "45%",
            boxShadow: "4px 6px 6px #aaa"
          }}
        >
          <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
            Total Hours Per Day This Week Vs Last Week
          </Typography>
          <BarChart
            margin={{
              left: 75,
              right: 50,
              top: 50,
              bottom: 50
            }}
            height={500}
            series={[
              {
                label: "This Week",
                color: "#e1b153",
                data: filteredMetrics.billingWeekdayQuantityThisWeek
              },
              {
                label: "Last Week",
                color: "#0c2d5f",
                data: filteredMetrics.billingWeekdayQuantityLastWeek
              }
            ]}
            layout="vertical"
            grid={{ vertical: true }}
            slots={{ legend: () => null }}
            xAxis={[
              {
                data: ["Sun", "Mon", "Tues", "Wed", "Thur", "Fri", "Sat"],
                scaleType: "band"
              }
            ]}
          />
        </Card>

        <Card
          sx={{
            marginTop: "50px",
            width: "45%",
            boxShadow: "4px 6px 6px #aaa"
          }}
        >
          <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
            Time Code Hours Per User This Week
          </Typography>
          <BarChart
            margin={{
              left: 125,
              right: 50,
              top: 50,
              bottom: 50
            }}
            height={500}
            series={filteredMetrics.timeCodeStackUser}
            layout="horizontal"
            grid={{ vertical: true }}
            slots={{ legend: () => null }}
            yAxis={[
              {
                data: filteredMetrics.billingUserArray,
                scaleType: "band"
              }
            ]}
          />
        </Card>

        <Card
          sx={{
            marginTop: "50px",
            width: "45%",
            boxShadow: "4px 6px 6px #aaa"
          }}
        >
          <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
            Time Code Hours Per Day This Week
          </Typography>
          <BarChart
            margin={{
              left: 75,
              right: 50,
              top: 50,
              bottom: 50
            }}
            height={500}
            series={filteredMetrics.timeCodeStackWeekday}
            layout="vertical"
            grid={{ vertical: true }}
            slots={{ legend: () => null }}
            xAxis={[
              {
                data: ["Sun", "Mon", "Tues", "Wed", "Thur", "Fri", "Sat"],
                scaleType: "band"
              }
            ]}
          />
        </Card>

        <Card
          sx={{
            marginTop: "50px",
            width: "70%",
            boxShadow: "4px 6px 6px #aaa"
          }}
        >
          <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
            Top Projects This Week
          </Typography>
          <Typography variant="h4" sx={{ padding: "5px 0 0 30px" }}>
            Total Hours: {Number(filteredMetrics.totalHoursThisWeek).toFixed(1)}
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
            <Box sx={{ width: "60%", height: 400 }}>
              <PieChart
                series={[
                  {
                    data: filteredMetrics.topProjectArray,
                    innerRadius: 10,
                    outerRadius: 150,
                    paddingAngle: 1,
                    cornerRadius: 5,
                    highlightScope: { faded: "global", highlighted: "item" },
                    arcLabel: (item) => `${Number(item.value).toFixed(1)}`,
                    arcLabelMinAngle: 45
                  }
                ]}
                height={400}
                colors={PALETTE}
                slotProps={{
                  legend: {
                    // Hide the default legend inside the chart
                    hidden: true
                  }
                }}
              />
            </Box>
            <Box sx={{ width: "40%" }}>
              {filteredMetrics.topProjectArray.map((item, index) => (
                <Box key={item.id} sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      backgroundColor: PALETTE[index % PALETTE.length],
                      mr: 1,
                      borderRadius: "2px"
                    }}
                  />
                  <Typography variant="body2">{item.label}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Card>
      </Stack>
    </>
  )
}

export default BillingAnalytics
