import { useState, useEffect } from "react"
import { Box, Typography, Card, LinearProgress } from "@mui/material"
import { Bar<PERSON>hart } from "@mui/x-charts"
import { PALETTE } from "./helpers/constants"
import { useInvoiceAnalytics } from "./firebaseMethods.js/getMethods"

function InvoiceData() {
  const [chartMetrics, setChartMetrics] = useState()

  const { data: invoiceAnalyticsData, isLoading: isInvoiceAnalyticsLoading } = useInvoiceAnalytics(PALETTE)

  useEffect(() => {
    if (invoiceAnalyticsData) {
      setChartMetrics(invoiceAnalyticsData)
    }
  }, [invoiceAnalyticsData])

  if (!chartMetrics || isInvoiceAnalyticsLoading) {
    return <LinearProgress color="primary" />
  }

  return (
    <Box
      sx={{
        bgcolor: "#F3f3f3",
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "space-around"
      }}
    >
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "150px",
          width: "75%",
          boxShadow: "4px 6px 6px #aaa"
        }}
      >
        <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
          Total Invoiced Over Last 12 Months
        </Typography>
        <BarChart
          margin={{
            left: 150,
            right: 50,
            top: 50,
            bottom: 50
          }}
          height={500}
          series={[
            {
              label: "Total Amount Paid",
              color: "#e1b153",
              data: chartMetrics.amountPaidArray
            },
            {
              label: "Total Amount Due",
              color: "#0c2d5f",
              data: chartMetrics.amountDueArray
            }
          ]}
          layout="vertical"
          grid={{ vertical: true }}
          slots={{ legend: () => null }}
          xAxis={[
            {
              data: chartMetrics.xAxisHashtagArray,
              scaleType: "band"
            }
          ]}
        />
      </Card>
    </Box>
  )
}

export default InvoiceData
