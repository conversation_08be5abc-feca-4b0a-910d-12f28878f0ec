import { render, screen, waitFor } from "@testing-library/react"
import ProjectAnalytics from "./ProjectAnalytics"
import { useProjectAnalytics } from "./firebaseMethods.js/getMethods"
import { PALETTE } from "./helpers/constants"

// Mock the imported hooks and components
jest.mock("./firebaseMethods.js/getMethods")
jest.mock("@mui/x-charts", () => ({
  LineChart: jest.fn(() => <div data-testid="line-chart">Line Chart</div>),
  PieChart: jest.fn(() => <div data-testid="pie-chart">Pie Chart</div>),
  BarChart: jest.fn(() => <div data-testid="bar-chart">Bar Chart</div>)
}))

// Mock fetch API (needed for Firebase)
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({}),
    ok: true
  })
)

// Mock Firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => Promise.resolve({ data: {} })),
  getFunctions: jest.fn()
}))

describe("ProjectAnalytics", () => {
  // Mock data for testing
  const mockChartData = {
    projectCountArray: [10, 15, 20, 25],
    xAxisMonthArray: ["Jan", "Feb", "Mar", "Apr"],
    primaryPhaseData: [{ data: [5, 10, 15] }],
    phaseData: ["Planning", "Development", "Testing"],
    primaryProjectData: [
      { id: "Developer 1", value: 5, label: "Developer 1" },
      { id: "Developer 2", value: 10, label: "Developer 2" }
    ],
    hashtagCountArray: [8, 12, 5],
    xAxisHashtagArray: ["#frontend", "#backend", "#design"]
  }

  // Test project type ID - changed to number
  const testProjectTypeId = 1

  afterEach(() => {
    jest.clearAllMocks()
  })

  test("renders loading state when data is loading", () => {
    useProjectAnalytics.mockReturnValue({
      data: null,
      isLoading: true
    })

    render(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Should show loading progress
    expect(screen.getByRole("progressbar")).toBeInTheDocument()
    expect(screen.queryByTestId("line-chart")).not.toBeInTheDocument()
  })

  test("renders loading state when data is null after loading", () => {
    useProjectAnalytics.mockReturnValue({
      data: null,
      isLoading: false
    })

    render(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Should still show loading progress when data is null
    expect(screen.getByRole("progressbar")).toBeInTheDocument()
  })

  test("renders all charts when data is loaded successfully", async () => {
    useProjectAnalytics.mockReturnValue({
      data: mockChartData,
      isLoading: false
    })

    render(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Should render all four charts
    expect(screen.getByText("Projects Created Over Last 12 Months")).toBeInTheDocument()
    expect(screen.getByText("Current Phase Count")).toBeInTheDocument()
    expect(screen.getByText("Project Primary Apportionment")).toBeInTheDocument()
    expect(screen.getByText("Hashtag Count")).toBeInTheDocument()

    // Check that charts are rendered
    expect(screen.getAllByTestId("bar-chart").length).toBe(2)
    expect(screen.getByTestId("line-chart")).toBeInTheDocument()
    expect(screen.getByTestId("pie-chart")).toBeInTheDocument()
  })

  test("calls useProjectAnalytics with correct parameters", () => {
    useProjectAnalytics.mockReturnValue({
      data: mockChartData,
      isLoading: false
    })

    render(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Check that the hook was called with the correct parameters
    expect(useProjectAnalytics).toHaveBeenCalledWith(testProjectTypeId, PALETTE)
  })

  test("updates chartMetrics when analyticsData changes", async () => {
    // First render with initial state
    useProjectAnalytics.mockReturnValue({
      data: null,
      isLoading: true
    })

    const { rerender } = render(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Then update with actual data
    useProjectAnalytics.mockReturnValue({
      data: mockChartData,
      isLoading: false
    })

    rerender(<ProjectAnalytics projectTypeId={testProjectTypeId} />)

    // Charts should now be visible
    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument()
      expect(screen.getAllByTestId("bar-chart").length).toBe(2)
    })
  })
})
