import { render, screen, fireEvent } from "@testing-library/react"
import ProjectTypeDropdown from "./ProjectTypeDropdown"
import { useProjectTypeList } from "@/helpers/firebaseGetMethods"

// Mock the hooks
jest.mock("../../../helpers/firebaseGetMethods")

// Mock fetch API (needed for Firebase)
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({}),
    ok: true
  })
)

// Mock Firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => Promise.resolve({ data: {} })),
  getFunctions: jest.fn()
}))

describe("ProjectTypeDropdown", () => {
  const mockSetSelectedTab = jest.fn()
  const mockSetProjectTypeId = jest.fn()

  const mockProjectTypeList = [
    { projectTypeId: { native: 1 }, name: "Project Type 1" },
    { projectTypeId: { native: 2 }, name: "Project Type 2" }
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Default mock implementation
    useProjectTypeList.mockReturnValue({
      data: mockProjectTypeList,
      isLoading: false
    })
  })

  test("renders loading state when project types are loading", () => {
    useProjectTypeList.mockReturnValue({
      data: [],
      isLoading: true
    })

    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    expect(screen.getByRole("progressbar")).toBeInTheDocument()
  })

  test("renders tabs correctly", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    expect(screen.getByRole("tab", { name: /project data/i })).toBeInTheDocument()
    expect(screen.getByRole("tab", { name: /time entry data/i })).toBeInTheDocument()
    expect(screen.getByRole("tab", { name: /invoice data/i })).toBeInTheDocument()
  })

  test("shows project type dropdown when selectedTab is 0", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    expect(screen.getByRole("combobox")).toBeInTheDocument()
    expect(screen.getByText("Project Type")).toBeInTheDocument()
  })

  test("does not show project type dropdown when selectedTab is not 0", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={1}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    expect(screen.queryByRole("combobox")).not.toBeInTheDocument()
    expect(screen.queryByText("Project Type")).not.toBeInTheDocument()
  })

  test("calls setSelectedTab when a tab is clicked", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    // Click on the second tab
    fireEvent.click(screen.getByRole("tab", { name: /time entry data/i }))

    expect(mockSetSelectedTab).toHaveBeenCalledWith(1)
  })

  test("renders project type options correctly", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    const select = screen.getByRole("combobox")
    expect(select).toHaveValue("1")

    // Verify options
    const options = screen.getAllByRole("option")
    expect(options).toHaveLength(2)
    expect(options[0]).toHaveTextContent("Project Type 1")
    expect(options[1]).toHaveTextContent("Project Type 2")
  })

  test("calls setProjectTypeId when project type is changed", () => {
    render(
      <ProjectTypeDropdown
        selectedTab={0}
        setSelectedTab={mockSetSelectedTab}
        projectTypeId={1}
        setProjectTypeId={mockSetProjectTypeId}
      />
    )

    const select = screen.getByRole("combobox")

    // Change the selected option
    fireEvent.change(select, { target: { value: "2" } })

    expect(mockSetProjectTypeId).toHaveBeenCalledWith("2")
  })
})
