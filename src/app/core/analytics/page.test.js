import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import Analytics from "./page"
import { useProjectTypeList, useTenant } from "../../../helpers/firebaseGetMethods"

// Mock all child components
jest.mock("./ProjectTypeDropdown", () => ({
  __esModule: true,
  default: jest.fn(({ _selectedTab, setSelectedTab, projectTypeId, setProjectTypeId }) => (
    <div data-testid="project-type-dropdown">
      <div data-testid="project-type-id">{projectTypeId || "NaN"}</div>
      <button data-testid="tab-0" onClick={() => setSelectedTab(0)}>
        Project Analytics
      </button>
      <button data-testid="tab-1" onClick={() => setSelectedTab(1)}>
        Billing Analytics
      </button>
      <button data-testid="tab-2" onClick={() => setSelectedTab(2)}>
        Invoice Data
      </button>
      <button data-testid="set-project-type" onClick={() => setProjectTypeId("999")}>
        Set Project Type
      </button>
    </div>
  ))
}))

jest.mock("./ProjectAnalytics", () => ({
  __esModule: true,
  default: jest.fn(({ projectTypeId }) => (
    <div data-testid="project-analytics">Project Analytics Content for type: {projectTypeId || "NaN"}</div>
  ))
}))

jest.mock("./BillingAnalytics", () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="billing-analytics">Billing Analytics Content</div>)
}))

jest.mock("./InvoiceData", () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="invoice-data">Invoice Data Content</div>)
}))

// Mock the hooks
jest.mock("../../../helpers/firebaseGetMethods")

// Mock fetch API (needed for Firebase)
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({}),
    ok: true
  })
)

// Mock Firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(() => Promise.resolve({ data: {} })),
  getFunctions: jest.fn()
}))

jest.mock("../settings/firebaseMethods.js/postMethods", () => ({
  useUpdateTenant: jest.fn().mockReturnValue({
    mutate: jest.fn(),
    isPending: false
  })
}))

describe("Analytics Page", () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Default mock implementations
    useProjectTypeList.mockReturnValue({
      data: [
        { projectTypeId: { native: "1" }, name: "Project Type 1" },
        { projectTypeId: { native: "2" }, name: "Project Type 2" }
      ],
      isLoading: false
    })

    useTenant.mockReturnValue({
      data: {
        userData: {
          tenant: "test-tenant",
          defaultProjectType: {
            "test-tenant": "1"
          }
        }
      },
      isLoading: false
    })
  })

  test("renders ProjectTypeDropdown initially", async () => {
    render(<Analytics />)
    await waitFor(() => {
      expect(screen.getByTestId("project-type-dropdown")).toBeInTheDocument()
    })
  })

  test("renders ProjectAnalytics by default (tab 0)", async () => {
    render(<Analytics />)

    await waitFor(() => {
      expect(screen.getByTestId("project-analytics")).toBeInTheDocument()
      expect(screen.queryByTestId("billing-analytics")).not.toBeInTheDocument()
      expect(screen.queryByTestId("invoice-data")).not.toBeInTheDocument()
    })
  })

  test("switches to BillingAnalytics when tab 1 is selected", async () => {
    render(<Analytics />)

    await waitFor(() => {
      expect(screen.getByTestId("project-analytics")).toBeInTheDocument()
    })

    fireEvent.click(screen.getByTestId("tab-1"))

    await waitFor(() => {
      expect(screen.queryByTestId("project-analytics")).not.toBeInTheDocument()
      expect(screen.getByTestId("billing-analytics")).toBeInTheDocument()
      expect(screen.queryByTestId("invoice-data")).not.toBeInTheDocument()
    })
  })

  test("switches to InvoiceData when tab 2 is selected", async () => {
    render(<Analytics />)

    await waitFor(() => {
      expect(screen.getByTestId("project-analytics")).toBeInTheDocument()
    })

    fireEvent.click(screen.getByTestId("tab-2"))

    await waitFor(() => {
      expect(screen.queryByTestId("project-analytics")).not.toBeInTheDocument()
      expect(screen.queryByTestId("billing-analytics")).not.toBeInTheDocument()
      expect(screen.getByTestId("invoice-data")).toBeInTheDocument()
    })
  })

  test("sets initial projectTypeId from tenant data", async () => {
    render(<Analytics />)

    // ProjectAnalytics should receive the projectTypeId from tenant data
    await waitFor(() => {
      expect(screen.getByTestId("project-analytics").textContent).toContain("type: 1")
    })
  })

  test("updates projectTypeId when changed", async () => {
    render(<Analytics />)

    await waitFor(() => {
      // Initial state
      expect(screen.getByTestId("project-type-id").textContent).toBe("1")
    })

    // Change project type
    fireEvent.click(screen.getByTestId("set-project-type"))

    await waitFor(() => {
      // Updated state should be passed to dropdown and analytics
      expect(screen.getByTestId("project-type-id").textContent).toBe("999")
      expect(screen.getByTestId("project-analytics").textContent).toContain("type: 999")
    })
  })

  test("handles missing tenant data gracefully", async () => {
    // Mock tenant data as null
    useTenant.mockReturnValue({
      data: null,
      isLoading: false
    })

    render(<Analytics />)

    // Should render the dropdown without errors
    await waitFor(() => {
      expect(screen.getByTestId("project-type-dropdown")).toBeInTheDocument()
    })

    // When tenant data is missing, we expect to see a loading indicator
    // and not the project analytics component
    const progressBar = screen.getByRole("progressbar")
    expect(progressBar).toBeInTheDocument()
    expect(screen.queryByTestId("project-analytics")).not.toBeInTheDocument()
  })

  test("handles tenant loading state", async () => {
    // Mock tenant data as loading
    useTenant.mockReturnValue({
      data: null,
      isLoading: true
    })

    render(<Analytics />)

    // Should render the dropdown without errors
    await waitFor(() => {
      expect(screen.getByTestId("project-type-dropdown")).toBeInTheDocument()
    })

    // When tenant data is loading, we expect to see a loading indicator
    // and not the project analytics component
    const progressBar = screen.getByRole("progressbar")
    expect(progressBar).toBeInTheDocument()
    expect(screen.queryByTestId("project-analytics")).not.toBeInTheDocument()
  })
})
