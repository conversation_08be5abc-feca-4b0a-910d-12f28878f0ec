import { useState, useEffect } from "react"
import { <PERSON>ack, Card } from "@mui/material"
import { useProjectList } from "@/helpers/firebaseGetMethods"
import { getPrimaryList } from "../kanban/helpers/projectPhaseClassifier"
import PrimaryPerformanceHeader from "./PrimaryPerformanceHeader"
import AlertSnackbar from "@/components/AlertSnackBar"

const PrimaryPerformance = () => {
  const [currentProjectPrimary, setCurrentProjectPrimary] = useState("All Primaries")
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [primaryList, setPrimaryList] = useState([])
  const { data: projectList, isError: projectsError } = useProjectList()

  useEffect(
    function extractsPrimaryList() {
      if (projectList?.projectData.length > 0) {
        setPrimaryList(getPrimaryList(projectList.projectData))
      }
    },
    [projectList]
  )

  useEffect(
    function handleErrors() {
      if (projectsError) {
        setAlertInfo({
          open: true,
          message: "There was an error loading the projects. Please try again later.",
          severity: "error"
        })
      }
    },
    [projectsError]
  )

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  return (
    <Stack
      sx={{
        bgcolor: "#F3f3f3",
        display: "flex",
        flexWrap: "wrap",
        width: "100%",
        justifyContent: "space-around"
      }}
    >
      <Card
        sx={{
          width: "calc(100% - 100px)",
          margin: "16px 50px",
          boxShadow: "4px 6px 6px #aaa",
          height: "calc(100vh - 220px)"
        }}
      >
        <PrimaryPerformanceHeader
          primaryList={primaryList || []}
          currentProjectPrimary={currentProjectPrimary}
          setCurrentProjectPrimary={setCurrentProjectPrimary}
        />
      </Card>
      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
      />
    </Stack>
  )
}

export default PrimaryPerformance
