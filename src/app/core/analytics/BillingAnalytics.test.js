import { httpsCallable } from "firebase/functions"
import { useQuery } from "@tanstack/react-query"
import {
  useProjectAnalytics,
  useBillingAnalytics,
  useInvoiceAnalytics
} from "./firebaseMethods.js/getMethods"

// Mock Firebase functions
jest.mock("firebase/functions", () => ({
  httpsCallable: jest.fn(),
  getFunctions: jest.fn(() => "mockedFunctions")
}))

// Mock React Query
jest.mock("@tanstack/react-query", () => ({
  useQuery: jest.fn()
}))

// Mock Firebase app
jest.mock("../../../app/firebase", () => "mockedApp")

describe("Firebase getMethods", () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Return mock value for useQuery to avoid executing the queryFn
    useQuery.mockReturnValue({
      data: "mockData",
      isLoading: false,
      error: null
    })
  })

  describe("useProjectAnalytics", () => {
    test("calls useQuery with correct parameters", () => {
      const mockProjectTypeId = "123"
      const mockPalette = ["#123", "#456"]
      const mockOptions = { staleTime: 5000 }

      // Setup mock httpsCallable
      httpsCallable.mockReturnValue(jest.fn())

      // Call the hook
      const result = useProjectAnalytics(mockProjectTypeId, mockPalette, mockOptions)

      // Check that useQuery was called correctly
      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["projectAnalytics", { projectTypeId: "123" }],
        queryFn: expect.any(Function),
        enabled: true,
        staleTime: 5000
      })

      // Check that the result is what useQuery returned
      expect(result).toEqual({
        data: "mockData",
        isLoading: false,
        error: null
      })
    })

    test("is disabled when projectTypeId is falsy", () => {
      useProjectAnalytics(null, ["#123"], {})

      // Should be disabled when projectTypeId is null
      expect(useQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false
        })
      )
    })

    test("queryFn handles successful response", async () => {
      // Setup mocks
      const mockResponse = { data: { results: "success" } }
      const mockCallable = jest.fn().mockResolvedValue(mockResponse)
      httpsCallable.mockReturnValue(mockCallable)

      // Call hook to capture the generated queryFn
      useProjectAnalytics("123", ["#123"], {})

      // Extract queryFn without executing it
      const queryFn = useQuery.mock.calls[0][0].queryFn

      // Now manually execute and test the queryFn
      const result = await queryFn()

      // Verify the callable was set up correctly
      expect(httpsCallable).toHaveBeenCalledWith("mockedFunctions", "getanalyticsdata")
      expect(mockCallable).toHaveBeenCalledWith({ projectTypeId: "123", palette: ["#123"] })

      // Verify result
      expect(result).toEqual({ results: "success" })
    })

    test("queryFn throws error when response data is missing", async () => {
      // Setup mock with null data
      const mockCallable = jest.fn().mockResolvedValue({ data: null })
      httpsCallable.mockReturnValue(mockCallable)

      // Call hook
      useProjectAnalytics("123", ["#123"], {})

      // Get queryFn
      const queryFn = useQuery.mock.calls[0][0].queryFn

      // Test error handling
      await expect(queryFn()).rejects.toThrow("Failed to fetch project analytics data")
    })
  })

  describe("useBillingAnalytics", () => {
    test("calls useQuery with correct parameters", () => {
      const mockPalette = ["#123", "#456"]
      const mockOptions = { refetchInterval: 30000 }

      // Call hook
      const result = useBillingAnalytics(mockPalette, mockOptions)

      // Check useQuery call
      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["billingAnalytics"],
        queryFn: expect.any(Function),
        refetchInterval: 30000
      })

      // Check result
      expect(result).toEqual({
        data: "mockData",
        isLoading: false,
        error: null
      })
    })

    test("queryFn handles successful response", async () => {
      // Setup mocks
      const mockResponse = { data: { billingData: "success" } }
      const mockCallable = jest.fn().mockResolvedValue(mockResponse)
      httpsCallable.mockReturnValue(mockCallable)

      // Call hook
      useBillingAnalytics(["#123"], {})

      // Extract queryFn
      const queryFn = useQuery.mock.calls[0][0].queryFn

      // Execute queryFn
      const result = await queryFn()

      // Verify callable setup
      expect(httpsCallable).toHaveBeenCalledWith("mockedFunctions", "getbillinganalyticsdata")
      expect(mockCallable).toHaveBeenCalledWith({ palette: ["#123"] })

      // Verify result
      expect(result).toEqual({ billingData: "success" })
    })

    test("queryFn throws error when response data is missing", async () => {
      const mockCallable = jest.fn().mockResolvedValue({ data: null })
      httpsCallable.mockReturnValue(mockCallable)

      useBillingAnalytics(["#123"], {})

      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch billing analytics data")
    })
  })

  describe("useInvoiceAnalytics", () => {
    test("calls useQuery with correct parameters", () => {
      const mockPalette = ["#123", "#456"]
      const mockOptions = { cacheTime: 60000 }

      const result = useInvoiceAnalytics(mockPalette, mockOptions)

      expect(useQuery).toHaveBeenCalledWith({
        queryKey: ["invoiceAnalytics"],
        queryFn: expect.any(Function),
        cacheTime: 60000
      })

      expect(result).toEqual({
        data: "mockData",
        isLoading: false,
        error: null
      })
    })

    test("queryFn handles successful response", async () => {
      const mockResponse = { data: { invoiceData: "success" } }
      const mockCallable = jest.fn().mockResolvedValue(mockResponse)
      httpsCallable.mockReturnValue(mockCallable)

      useInvoiceAnalytics(["#123"], {})

      const queryFn = useQuery.mock.calls[0][0].queryFn

      const result = await queryFn()

      expect(httpsCallable).toHaveBeenCalledWith("mockedFunctions", "getinvoiceanalyticsdata")
      expect(mockCallable).toHaveBeenCalledWith({ palette: ["#123"] })

      expect(result).toEqual({ invoiceData: "success" })
    })

    test("queryFn throws error when response data is missing", async () => {
      const mockCallable = jest.fn().mockResolvedValue({ data: null })
      httpsCallable.mockReturnValue(mockCallable)

      useInvoiceAnalytics(["#123"], {})

      const queryFn = useQuery.mock.calls[0][0].queryFn

      await expect(queryFn()).rejects.toThrow("Failed to fetch invoice analytics data")
    })
  })
})
