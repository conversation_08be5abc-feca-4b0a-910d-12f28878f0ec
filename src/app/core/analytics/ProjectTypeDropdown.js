import { NativeSelect, Box, Typography, Tabs, Tab, LinearProgress } from "@mui/material"
import { useProjectTypeList } from "@/helpers/firebaseGetMethods"
import { func, number } from "prop-types"

function ProjectTypeDropdown({ selectedTab, setSelectedTab, projectTypeId, setProjectTypeId }) {
  const { data: projectTypeList, isLoading: areProjectTypesLoading } = useProjectTypeList()

  const selectTab = (_e, newTab) => {
    setSelectedTab(newTab)
  }

  const selectProjectType = (e) => {
    setProjectTypeId(e.target.value)
  }

  if (areProjectTypesLoading) {
    return <LinearProgress color="primary" />
  }

  return (
    <Box
      sx={{
        padding: "30px",
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center"
      }}
    >
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs value={selectedTab} onChange={selectTab} aria-label="basic tabs example">
          <Tab label="Project Data" />
          <Tab label="Time Entry Data" />
          <Tab label="Invoice Data" />
          {/* <Tab label="Primary Performance" /> */}
        </Tabs>
      </Box>
      {selectedTab === 0 && projectTypeId ? (
        <Box sx={{ width: 250, marginLeft: "30px" }}>
          <Typography variant="h6">Project Type</Typography>
          <NativeSelect sx={{ width: 200 }} defaultValue={projectTypeId} onChange={selectProjectType}>
            {projectTypeList.map((type) => (
              <option key={type.projectTypeId.native} value={type.projectTypeId.native}>
                {type.name}
              </option>
            ))}
          </NativeSelect>
        </Box>
      ) : (
        <></>
      )}
    </Box>
  )
}

export default ProjectTypeDropdown

ProjectTypeDropdown.propTypes = {
  selectedTab: number.isRequired,
  setSelectedTab: func.isRequired,
  projectTypeId: number.isRequired,
  setProjectTypeId: func.isRequired
}
