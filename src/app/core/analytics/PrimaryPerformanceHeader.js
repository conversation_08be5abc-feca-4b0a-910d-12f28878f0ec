import { Typography, Stack, CircularProgress, NativeSelect } from "@mui/material"
import { string, arrayOf, func } from "prop-types"

const PrimaryPerformanceHeader = ({ primaryList, currentProjectPrimary, setCurrentProjectPrimary }) => {
  const selectPrimary = (e) => {
    setCurrentProjectPrimary(e.target.value)
  }
  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ padding: "30px" }}>
      <Typography variant="h4">Primary Performance</Typography>
      <Stack direction="row" justifyContent="space-between" alignItems="center" gap={2}>
        <Typography variant="h6">Primaries List</Typography>
        {primaryList.length === 0 ? (
          <CircularProgress />
        ) : (
          <NativeSelect value={currentProjectPrimary} onChange={selectPrimary} sx={{ width: 200 }}>
            <option value={"All Primaries"}>All Primaries</option>
            {primaryList.map((username) => (
              <option key={username} value={username}>
                {username}
              </option>
            ))}
          </NativeSelect>
        )}
      </Stack>
    </Stack>
  )
}

PrimaryPerformanceHeader.propTypes = {
  primaryList: arrayOf(string).isRequired,
  currentProjectPrimary: string.isRequired,
  setCurrentProjectPrimary: func.isRequired
}

export default PrimaryPerformanceHeader
