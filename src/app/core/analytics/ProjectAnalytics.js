import { useState, useEffect } from "react"
import { <PERSON>, Typo<PERSON>, Card, LinearProgress } from "@mui/material"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/x-charts"
import { PALETTE } from "./helpers/constants"
import { useProjectAnalytics } from "./firebaseMethods.js/getMethods"
import { number } from "prop-types"

function ProjectAnalytics({ projectTypeId }) {
  const [chartMetrics, setChartMetrics] = useState()

  const { data: analyticsData, isLoading: isAnalyticsLoading } = useProjectAnalytics(projectTypeId, PALETTE)

  useEffect(() => {
    if (analyticsData) {
      setChartMetrics(analyticsData)
    }
  }, [analyticsData])

  if (isAnalyticsLoading || !chartMetrics) {
    return <LinearProgress color="primary" sx={{ marginTop: "32px" }} />
  }

  return (
    <Box
      sx={{
        bgcolor: "#F3f3f3",
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "space-around"
      }}
    >
      <Card
        sx={{
          marginTop: "50px",
          width: "48%",
          boxShadow: "4px 6px 6px #aaa"
        }}
      >
        <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
          Projects Created Over Last 12 Months
        </Typography>
        <LineChart
          margin={{
            left: 50,
            right: 50,
            top: 50,
            bottom: 50
          }}
          series={[{ color: "#e1b153", data: chartMetrics?.projectCountArray }]}
          grid={{ horizontal: true }}
          height={500}
          xAxis={[
            {
              data: chartMetrics?.xAxisMonthArray,
              scaleType: "point"
            }
          ]}
        />
      </Card>
      <Card
        sx={{
          marginTop: "50px",
          width: "48%",
          boxShadow: "4px 6px 6px #aaa"
        }}
      >
        <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
          Current Phase Count
        </Typography>
        <BarChart
          margin={{
            left: 150,
            right: 50,
            top: 50,
            bottom: 50
          }}
          height={500}
          series={chartMetrics?.primaryPhaseData}
          layout="horizontal"
          grid={{ vertical: true }}
          slots={{ legend: () => null }}
          yAxis={[
            {
              data: chartMetrics?.phaseData,
              scaleType: "band"
            }
          ]}
        />
      </Card>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "50px",
          width: "48%",
          boxShadow: "4px 6px 6px #aaa"
        }}
      >
        <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
          Project Primary Apportionment
        </Typography>
        <PieChart
          series={[
            {
              data: chartMetrics?.primaryProjectData,
              innerRadius: 1,
              outerRadius: 180,
              paddingAngle: 0,
              cornerRadius: 5,
              cx: 220
            }
          ]}
          height={600}
          colors={PALETTE}
        />
      </Card>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "50px",
          width: "48%",
          boxShadow: "4px 6px 6px #aaa"
        }}
      >
        <Typography variant="h4" sx={{ padding: "30px 0 0 30px" }}>
          Hashtag Count
        </Typography>
        <BarChart
          margin={{
            left: 175,
            right: 50,
            top: 50,
            bottom: 50
          }}
          height={600}
          series={[{ color: "#e1b153", data: chartMetrics?.hashtagCountArray }]}
          layout="horizontal"
          grid={{ vertical: true }}
          slots={{ legend: () => null }}
          yAxis={[
            {
              data: chartMetrics?.xAxisHashtagArray,
              scaleType: "band"
            }
          ]}
        />
      </Card>
    </Box>
  )
}

export default ProjectAnalytics

ProjectAnalytics.propTypes = {
  projectTypeId: number.isRequired
}
