import React from "react"
import { render, screen, waitFor } from "@testing-library/react"
import InvoiceData from "./InvoiceData"
import { useInvoiceAnalytics } from "./firebaseMethods.js/getMethods"
import { BarChart } from "@mui/x-charts"

// Mock the hooks and chart components
jest.mock("./firebaseMethods.js/getMethods", () => ({
  useInvoiceAnalytics: jest.fn()
}))

jest.mock("@mui/x-charts", () => ({
  BarChart: jest.fn(() => <div data-testid="bar-chart" />)
}))

jest.mock("@mui/material", () => {
  const actual = jest.requireActual("@mui/material")
  return {
    ...actual,
    LinearProgress: jest.fn(() => <div data-testid="linear-progress" />),
    Box: jest.fn(({ children, sx }) => <div data-testid="mui-box">{children}</div>),
    Typography: jest.fn(({ children, variant, sx }) => (
      <div data-testid={`typography-${variant || "default"}`}>{children}</div>
    )),
    Card: jest.fn(({ children, sx }) => <div data-testid="mui-card">{children}</div>)
  }
})

describe("InvoiceData", () => {
  const mockInvoiceData = {
    amountPaidArray: [5000, 7500, 6200, 8000, 9500, 7000, 8500, 9000, 7200, 8300, 10000, 12000],
    amountDueArray: [1000, 500, 800, 1200, 700, 900, 600, 1100, 800, 500, 900, 1500],
    xAxisHashtagArray: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test("renders loading state when isInvoiceAnalyticsLoading is true", () => {
    // Mock loading state
    useInvoiceAnalytics.mockReturnValue({
      data: undefined,
      isLoading: true
    })

    render(<InvoiceData />)

    // Should show linear progress
    expect(screen.getByTestId("linear-progress")).toBeInTheDocument()

    // Chart should not be rendered
    expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument()
  })

  test("renders loading state when chartMetrics is not available", () => {
    // Mock state with no data
    useInvoiceAnalytics.mockReturnValue({
      data: null,
      isLoading: false
    })

    render(<InvoiceData />)

    // Should show linear progress
    expect(screen.getByTestId("linear-progress")).toBeInTheDocument()
  })

  test("renders chart when data is available", async () => {
    // Mock successful data load
    useInvoiceAnalytics.mockReturnValue({
      data: mockInvoiceData,
      isLoading: false
    })

    render(<InvoiceData />)

    // LinearProgress should not be rendered
    expect(screen.queryByTestId("linear-progress")).not.toBeInTheDocument()

    // Verify that chart is rendered
    expect(screen.getByTestId("bar-chart")).toBeInTheDocument()

    // Verify heading element is rendered with correct text
    expect(screen.getByText("Total Invoiced Over Last 12 Months")).toBeInTheDocument()

    // Check that the bar chart was called with the correct data
    expect(BarChart).toHaveBeenCalledWith(
      expect.objectContaining({
        series: [
          expect.objectContaining({
            label: "Total Amount Paid",
            color: "#e1b153",
            data: mockInvoiceData.amountPaidArray
          }),
          expect.objectContaining({
            label: "Total Amount Due",
            color: "#0c2d5f",
            data: mockInvoiceData.amountDueArray
          })
        ],
        xAxis: [
          expect.objectContaining({
            data: mockInvoiceData.xAxisHashtagArray,
            scaleType: "band"
          })
        ]
      }),
      expect.anything()
    )
  })

  test("useEffect updates chartMetrics when invoiceAnalyticsData changes", async () => {
    // Initial render with loading state
    useInvoiceAnalytics.mockReturnValue({
      data: null,
      isLoading: true
    })

    const { rerender } = render(<InvoiceData />)

    // Verify loading state
    expect(screen.getByTestId("linear-progress")).toBeInTheDocument()

    // Update the mock to return data
    useInvoiceAnalytics.mockReturnValue({
      data: mockInvoiceData,
      isLoading: false
    })

    // Re-render the component
    rerender(<InvoiceData />)

    // Now chart should be rendered
    await waitFor(() => {
      expect(screen.getByTestId("bar-chart")).toBeInTheDocument()
    })
  })

  test("properly sets up chart dimensions and styling", () => {
    // Mock successful data load
    useInvoiceAnalytics.mockReturnValue({
      data: mockInvoiceData,
      isLoading: false
    })

    render(<InvoiceData />)

    // Verify the card styling and chart dimensions by checking BarChart props
    expect(BarChart).toHaveBeenCalledWith(
      expect.objectContaining({
        margin: {
          left: 150,
          right: 50,
          top: 50,
          bottom: 50
        },
        height: 500,
        layout: "vertical",
        grid: { vertical: true }
      }),
      expect.anything()
    )
  })
})
