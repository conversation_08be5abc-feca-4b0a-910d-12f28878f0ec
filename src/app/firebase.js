import { initializeApp } from "firebase/app"
import { getAuth } from "firebase/auth"
import { getFirestore } from "firebase/firestore"

const firebaseConfig = {
  apiKey: "AIzaSyAeCT0f2rr_7uRxdZb_0ZRGeC5xzrVhdrg",
  authDomain: "rops-kanban.firebaseapp.com",
  projectId: "rops-kanban",
  storageBucket: "rops-kanban.appspot.com",
  messagingSenderId: "564388405283",
  appId: "1:564388405283:web:914b41fb0dbabda0481196",
  measurementId: "G-R8N9FV13GJ"
}

const development = {
  apiKey: "AIzaSyBFHE7bmnaO0aIbYEwzFEC7JbQurkk-mhY",
  authDomain: "dev-env-rops-kanban.firebaseapp.com",
  projectId: "dev-env-rops-kanban",
  storageBucket: "dev-env-rops-kanban.firebasestorage.app",
  messagingSenderId: "600049542945",
  appId: "1:600049542945:web:cf2415873add58f23fc060",
  measurementId: "G-1T0TV8B4H3"
}

const app = initializeApp(firebaseConfig)

export const auth = getAuth(app)
export const db = getFirestore(app)

export default app
