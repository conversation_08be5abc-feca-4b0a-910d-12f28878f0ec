#!/bin/bash

# Add all changes
git add .

# Check if development Firebase config is being used
if grep -q "const app = initializeApp(development)" "src/app/firebase.js"; then
    echo "###### ######################################################### ######"
    echo "###### ALERT: Development environment detected!                   ######"
    echo "###### Do you want to switch to production environment? (y/n):   ######"
    echo "###### ######################################################### ######"
    
    read -n 1 -r answer
    echo ""
    
    if [[ $answer =~ ^[Yy]$ ]]; then
        # Switch to production environment
        sed -i '' 's/const app = initializeApp(development)/const app = initializeApp(firebaseConfig)/g' src/app/firebase.js
        echo "✓ Switched to production environment."
        # Stage the environment change
        git add .
    else
        echo "###### ######################################################### ######"
        echo "###### ######################################################### ######"
        echo "######                                                           ######"
        echo "###### ERROR: Development Firebase config detected. Cannot       ######"
        echo "###### proceed with push while in development environment.      ######"
        echo "######                                                           ######"
        echo "###### ######################################################### ######"
        echo "###### ######################################################### ######"
        exit 1
    fi
fi

# Prompt for commit message
echo "Enter commit message:"
read message

# Commit with the provided message
git commit -m "$message"

# Run tests
npm run test

# Check if tests failed
if [ $? -ne 0 ]; then
    echo "###### ######################################################### ######"
    echo "###### ######################################################### ######"
    echo "######                                                           ######"
    echo "###### ERROR: Some tests failed. Please fix them before pushing. ######"
    echo "######                                                           ######"
    echo "###### ######################################################### ######"
    echo "###### ######################################################### ######"
    exit 1
fi

# Run build
echo "Running build to check for errors..."
npm run build > build_log.txt 2>&1

# Check if build failed
if [ $? -ne 0 ]; then
    # If build failed, show error and log details
    echo "###### ######################################################### ######"
    echo "###### ######################################################### ######"
    echo "######                                                           ######"
    echo "###### ERROR: Build failed. Please fix errors before pushing.    ######"
    echo "######                                                           ######"
    echo "###### ######################################################### ######"
    echo "###### ######################################################### ######"
    
    # Extract the error location and message together
    echo -e "\nAffected page:"
    error_location=$(grep -B 1 "Error:" build_log.txt | head -n 1)
    echo "${error_location}"
    
    # Find the actual error message
    echo -e "\nError type:"
    grep "Error:" build_log.txt | head -1
    
    # Display hint about the error
    echo -e "\nSuggested action:"
    if grep -q "display name" build_log.txt; then
        echo "Add a display name to the component in ${error_location}"
        echo "Example: ComponentName.displayName = 'ComponentName'"
    elif grep -q "undefined.*map" build_log.txt; then
        echo "Check for undefined arrays that are being mapped over in your code."
    elif grep -q "Cannot find module" build_log.txt; then
        echo "Check for missing dependencies or incorrect import paths."
    else
        echo "Review the code in the affected page for potential issues."
    fi
    exit 1
fi

# If we've made it here, all tests and build passed - push the changes
echo ""
echo "✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓"
echo "✓                                                     ✓"
echo "✓  SUCCESS! Tests passed, build succeeded, and code   ✓"
echo "✓  will be pushed to the repository.                  ✓"
echo "✓                                                     ✓"
echo "✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓✓"
echo ""
git push
